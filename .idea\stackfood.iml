<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravelpkg/laravelchk" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/gregwar/captcha" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/serializable-closure" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mpdf/psr-http-message-shim" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/aws/aws-crt-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/aws/aws-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/flysystem-aws-s3-v3" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mtdowling/jmespath.php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/beste/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/beste/in-memory-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/beste/json" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/google/auth" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/google/cloud-core" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/google/cloud-storage" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/google/common-protos" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/google/gax" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/google/grpc-gcp" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/google/longrunning" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/google/protobuf" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/grpc/grpc" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/kreait/firebase-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/kreait/firebase-tokens" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/rize/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/barryvdh/laravel-debugbar" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/beyondcode/laravel-websockets" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/brian2694/laravel-toastr" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/carbonphp/carbon-doctrine-types" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/cboden/ratchet" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/defuse/php-encryption" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/dflydev/dot-access-data" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/dbal" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/deprecations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/event-manager" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/evenement/evenement" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ezyang/htmlpurifier" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/facade/ignition-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fig/http-message-util" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/filp/whoops" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/firebase/php-jwt" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/hamcrest/hamcrest-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/intervention/image" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/iyzico/iyzipay-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/framework" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/passport" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/prompts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/sail" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/tinker" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lcobucci/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lcobucci/jwt" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/commonmark" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/config" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/event" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/flysystem-local" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/oauth2-server" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/uri" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/uri-interfaces" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/maatwebsite/excel" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/madnest/madzipper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/maennchen/zipstream-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/markbaker/complex" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/markbaker/matrix" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/matanyadaev/laravel-eloquent-spatial" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/maximebf/debugbar" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mercadopago/dx-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mockery/mockery" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mpdf/mpdf" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mpdf/psr-log-aware-trait" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nette/schema" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nette/utils" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nunomaduro/collision" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nunomaduro/termwind" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nwidart/laravel-modules" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nyholm/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/openspout/openspout" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paragonie/constant_time_encoding" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paragonie/sodium_compat" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paypal/paypal-checkout-sdk" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paypal/paypalhttp" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phayes/geophp" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpoffice/phpspreadsheet" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpseclib/phpseclib" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psy/psysh" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/pusher/pusher-php-server" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/rap2hpoutre/fast-excel" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ratchet/rfc6455" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/razorpay/razorpay" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/react/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/react/dns" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/react/event-loop" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/react/http" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/react/promise" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/react/socket" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/react/stream" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/rmccue/requests" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/setasign/fpdi" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spatie/backtrace" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spatie/error-solutions" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spatie/flare-client-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spatie/ignition" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spatie/laravel-ignition" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/stripe/stripe-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/psr-http-message-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/tijsverkoyen/css-to-inline-styles" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/twilio/sdk" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/unicodeveloper/laravel-paystack" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/webmozart/assert" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/xendit/xendit-php" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>