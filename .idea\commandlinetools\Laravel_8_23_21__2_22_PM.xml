<?xml version="1.0" encoding="UTF-8"?>
<framework xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="schemas/frameworkDescriptionVersion1.1.4.xsd" frameworkId="com.laravel.component" name="Laravel_8/23/21, 2:22 PM" invoke="/opt/lampp/htdocs/stackfood/artisan" alias="artisan" enabled="true" version="2">
  <extraData><![CDATA[version:3]]></extraData>
  <command>
    <name>clear-compiled</name>
    <help><![CDATA[Remove the compiled class file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>db</name>
    <help><![CDATA[Start a new database CLI session<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--read</td><td></td><td>Connect to the read connection</td></tr> <tr><td>--write</td><td></td><td>Connect to the write connection</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>connection[=null]</params>
    <optionsBefore>
      <option name="--read" shortcut="">
        <help><![CDATA[Connect to the read connection]]></help>
      </option>
      <option name="--write" shortcut="">
        <help><![CDATA[Connect to the write connection]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>down</name>
    <help><![CDATA[Put the application into maintenance / demo mode<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--redirect</td><td></td><td>The path that users should be redirected to</td></tr> <tr><td>--render</td><td></td><td>The view that should be prerendered for display during maintenance mode</td></tr> <tr><td>--retry</td><td></td><td>The number of seconds after which the request may be retried</td></tr> <tr><td>--refresh</td><td></td><td>The number of seconds after which the browser may refresh</td></tr> <tr><td>--secret</td><td></td><td>The secret phrase that may be used to bypass maintenance mode</td></tr> <tr><td>--status</td><td></td><td>The status code that should be used when returning the maintenance mode response</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--redirect" shortcut="" pattern="equals">
        <help><![CDATA[The path that users should be redirected to]]></help>
      </option>
      <option name="--render" shortcut="" pattern="equals">
        <help><![CDATA[The view that should be prerendered for display during maintenance mode]]></help>
      </option>
      <option name="--retry" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds after which the request may be retried]]></help>
      </option>
      <option name="--refresh" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds after which the browser may refresh]]></help>
      </option>
      <option name="--secret" shortcut="" pattern="equals">
        <help><![CDATA[The secret phrase that may be used to bypass maintenance mode]]></help>
      </option>
      <option name="--status" shortcut="" pattern="equals">
        <help><![CDATA[The status code that should be used when returning the maintenance mode response]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>env</name>
    <help><![CDATA[Display the current framework environment<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>help</name>
    <help><![CDATA[The <b>help</b> command displays help for a given command:<br> <br> <b>/opt/lampp/htdocs/stackfood/artisan help list</b><br> <br> You can also output the help in other formats by using the <comment>--format</comment> option:<br> <br> <b>/opt/lampp/htdocs/stackfood/artisan help --format=xml list</b><br> <br> To display the list of available commands, please use the <b>list</b> command.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--format</td><td></td><td>The output format (txt, xml, json, or md)</td></tr> <tr><td>--raw</td><td></td><td>To output raw command help</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>command_name[=null]</params>
    <optionsBefore>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format (txt, xml, json, or md)]]></help>
      </option>
      <option name="--raw" shortcut="">
        <help><![CDATA[To output raw command help]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>inspire</name>
    <help><![CDATA[Display an inspiring quote<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>list</name>
    <help><![CDATA[The <b>list</b> command lists all commands:<br> <br> <b>/opt/lampp/htdocs/stackfood/artisan list</b><br> <br> You can also display the commands for a specific namespace:<br> <br> <b>/opt/lampp/htdocs/stackfood/artisan list test</b><br> <br> You can also output the information in other formats by using the <comment>--format</comment> option:<br> <br> <b>/opt/lampp/htdocs/stackfood/artisan list --format=xml</b><br> <br> It's also possible to get raw list of commands (useful for embedding command runner):<br> <br> <b>/opt/lampp/htdocs/stackfood/artisan list --raw</b><br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--raw</td><td></td><td>To output raw command list</td></tr> <tr><td>--format</td><td></td><td>The output format (txt, xml, json, or md)</td></tr> <tr><td>--short</td><td></td><td>To skip describing commands' arguments</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>namespace[=null]</params>
    <optionsBefore>
      <option name="--raw" shortcut="">
        <help><![CDATA[To output raw command list]]></help>
      </option>
      <option name="--format" shortcut="" pattern="equals">
        <help><![CDATA[The output format (txt, xml, json, or md)]]></help>
      </option>
      <option name="--short" shortcut="">
        <help><![CDATA[To skip describing commands' arguments]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate</name>
    <help><![CDATA[Run the database migrations<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to be executed</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--schema-path</td><td></td><td>The path to a schema dump file</td></tr> <tr><td>--pretend</td><td></td><td>Dump the SQL queries that would be run</td></tr> <tr><td>--seed</td><td></td><td>Indicates if the seed task should be re-run</td></tr> <tr><td>--step</td><td></td><td>Force the migrations to be run so they can be rolled back individually</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to be executed]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--schema-path" shortcut="" pattern="equals">
        <help><![CDATA[The path to a schema dump file]]></help>
      </option>
      <option name="--pretend" shortcut="">
        <help><![CDATA[Dump the SQL queries that would be run]]></help>
      </option>
      <option name="--seed" shortcut="">
        <help><![CDATA[Indicates if the seed task should be re-run]]></help>
      </option>
      <option name="--step" shortcut="">
        <help><![CDATA[Force the migrations to be run so they can be rolled back individually]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>optimize</name>
    <help><![CDATA[Cache the framework bootstrap files<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>serve</name>
    <help><![CDATA[Serve the application on the PHP development server<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--host</td><td></td><td>The host address to serve the application on</td></tr> <tr><td>--port</td><td></td><td>The port to serve the application on</td></tr> <tr><td>--tries</td><td></td><td>The max number of ports to attempt to serve from</td></tr> <tr><td>--no-reload</td><td></td><td>Do not reload the development server on .env file changes</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--host" shortcut="" pattern="equals">
        <help><![CDATA[The host address to serve the application on]]></help>
      </option>
      <option name="--port" shortcut="" pattern="equals">
        <help><![CDATA[The port to serve the application on]]></help>
      </option>
      <option name="--tries" shortcut="" pattern="equals">
        <help><![CDATA[The max number of ports to attempt to serve from]]></help>
      </option>
      <option name="--no-reload" shortcut="">
        <help><![CDATA[Do not reload the development server on .env file changes]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>test</name>
    <help><![CDATA[Run the application tests<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--without-tty</td><td></td><td>Disable output to TTY</td></tr> <tr><td>--parallel</td><td>(-p)</td><td>Indicates if the tests should run in parallel</td></tr> <tr><td>--recreate-databases</td><td></td><td>Indicates if the test databases should be re-created</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--without-tty" shortcut="">
        <help><![CDATA[Disable output to TTY]]></help>
      </option>
      <option name="--parallel" shortcut="-p">
        <help><![CDATA[Indicates if the tests should run in parallel]]></help>
      </option>
      <option name="--recreate-databases" shortcut="">
        <help><![CDATA[Indicates if the test databases should be re-created]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>tinker</name>
    <help><![CDATA[Interact with your application<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--execute</td><td></td><td>Execute the given code using Tinker</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>include[=null]</params>
    <optionsBefore>
      <option name="--execute" shortcut="" pattern="equals">
        <help><![CDATA[Execute the given code using Tinker]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>up</name>
    <help><![CDATA[Bring the application out of maintenance mode<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>auth:clear-resets</name>
    <help><![CDATA[Flush expired password reset tokens<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:clear</name>
    <help><![CDATA[Flush the application cache<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--tags</td><td></td><td>The cache tags you would like to clear</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>store[=null]</params>
    <optionsBefore>
      <option name="--tags" shortcut="" pattern="equals">
        <help><![CDATA[The cache tags you would like to clear]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:forget</name>
    <help><![CDATA[Remove an item from the cache<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>key store[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>cache:table</name>
    <help><![CDATA[Create a migration for the cache database table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>config:cache</name>
    <help><![CDATA[Create a cache file for faster configuration loading<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>config:clear</name>
    <help><![CDATA[Remove the configuration cache file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>database:refresh</name>
    <help><![CDATA[Refresh database after a certain time<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>db:seed</name>
    <help><![CDATA[Seed the database with records<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--class</td><td></td><td>The class name of the root seeder</td></tr> <tr><td>--database</td><td></td><td>The database connection to seed</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>class[=null]</params>
    <optionsBefore>
      <option name="--class" shortcut="" pattern="equals">
        <help><![CDATA[The class name of the root seeder]]></help>
      </option>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to seed]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>db:wipe</name>
    <help><![CDATA[Drop all tables, views, and types<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--drop-views</td><td></td><td>Drop all tables and views</td></tr> <tr><td>--drop-types</td><td></td><td>Drop all tables and types (Postgres only)</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--drop-views" shortcut="">
        <help><![CDATA[Drop all tables and views]]></help>
      </option>
      <option name="--drop-types" shortcut="">
        <help><![CDATA[Drop all tables and types (Postgres only)]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>debugbar:clear</name>
    <help><![CDATA[Clear the Debugbar Storage<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>event:cache</name>
    <help><![CDATA[Discover and cache the application's events and listeners<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>event:clear</name>
    <help><![CDATA[Clear all cached events and listeners<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>event:generate</name>
    <help><![CDATA[Generate the missing events and listeners based on registration<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>event:list</name>
    <help><![CDATA[List the application's events and listeners<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--event</td><td></td><td>Filter the events by name</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--event" shortcut="" pattern="equals">
        <help><![CDATA[Filter the events by name]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>key:generate</name>
    <help><![CDATA[Set the application key<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--show</td><td></td><td>Display the key instead of modifying files</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--show" shortcut="">
        <help><![CDATA[Display the key instead of modifying files]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:cast</name>
    <help><![CDATA[Create a new custom Eloquent cast class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:channel</name>
    <help><![CDATA[Create a new channel class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:command</name>
    <help><![CDATA[Create a new Artisan command<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--command</td><td></td><td>The terminal command that should be assigned</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--command" shortcut="" pattern="equals">
        <help><![CDATA[The terminal command that should be assigned]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:component</name>
    <help><![CDATA[Create a new view component class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td></td><td>Create the class even if the component already exists</td></tr> <tr><td>--inline</td><td></td><td>Create a component that renders an inline view</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="">
        <help><![CDATA[Create the class even if the component already exists]]></help>
      </option>
      <option name="--inline" shortcut="">
        <help><![CDATA[Create a component that renders an inline view]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:controller</name>
    <help><![CDATA[Create a new controller class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--api</td><td></td><td>Exclude the create and edit methods from the controller.</td></tr> <tr><td>--type</td><td></td><td>Manually specify the controller stub file to use.</td></tr> <tr><td>--force</td><td></td><td>Create the class even if the controller already exists</td></tr> <tr><td>--invokable</td><td>(-i)</td><td>Generate a single method, invokable controller class.</td></tr> <tr><td>--model</td><td>(-m)</td><td>Generate a resource controller for the given model.</td></tr> <tr><td>--parent</td><td>(-p)</td><td>Generate a nested resource controller class.</td></tr> <tr><td>--resource</td><td>(-r)</td><td>Generate a resource controller class.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--api" shortcut="">
        <help><![CDATA[Exclude the create and edit methods from the controller.]]></help>
      </option>
      <option name="--type" shortcut="" pattern="equals">
        <help><![CDATA[Manually specify the controller stub file to use.]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Create the class even if the controller already exists]]></help>
      </option>
      <option name="--invokable" shortcut="-i">
        <help><![CDATA[Generate a single method, invokable controller class.]]></help>
      </option>
      <option name="--model" shortcut="-m" pattern="equals">
        <help><![CDATA[Generate a resource controller for the given model.]]></help>
      </option>
      <option name="--parent" shortcut="-p" pattern="equals">
        <help><![CDATA[Generate a nested resource controller class.]]></help>
      </option>
      <option name="--resource" shortcut="-r">
        <help><![CDATA[Generate a resource controller class.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:event</name>
    <help><![CDATA[Create a new event class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:exception</name>
    <help><![CDATA[Create a new custom exception class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--render</td><td></td><td>Create the exception with an empty render method</td></tr> <tr><td>--report</td><td></td><td>Create the exception with an empty report method</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--render" shortcut="">
        <help><![CDATA[Create the exception with an empty render method]]></help>
      </option>
      <option name="--report" shortcut="">
        <help><![CDATA[Create the exception with an empty report method]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:factory</name>
    <help><![CDATA[Create a new model factory<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--model</td><td>(-m)</td><td>The name of the model</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--model" shortcut="-m" pattern="equals">
        <help><![CDATA[The name of the model]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:job</name>
    <help><![CDATA[Create a new job class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--sync</td><td></td><td>Indicates that job should be synchronous</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--sync" shortcut="">
        <help><![CDATA[Indicates that job should be synchronous]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:listener</name>
    <help><![CDATA[Create a new event listener class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--event</td><td>(-e)</td><td>The event class being listened for</td></tr> <tr><td>--queued</td><td></td><td>Indicates the event listener should be queued</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--event" shortcut="-e" pattern="equals">
        <help><![CDATA[The event class being listened for]]></help>
      </option>
      <option name="--queued" shortcut="">
        <help><![CDATA[Indicates the event listener should be queued]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:mail</name>
    <help><![CDATA[Create a new email class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the mailable already exists</td></tr> <tr><td>--markdown</td><td>(-m)</td><td>Create a new Markdown template for the mailable</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the mailable already exists]]></help>
      </option>
      <option name="--markdown" shortcut="-m" pattern="equals">
        <help><![CDATA[Create a new Markdown template for the mailable]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:middleware</name>
    <help><![CDATA[Create a new middleware class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:migration</name>
    <help><![CDATA[Create a new migration file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--create</td><td></td><td>The table to be created</td></tr> <tr><td>--table</td><td></td><td>The table to migrate</td></tr> <tr><td>--path</td><td></td><td>The location where the migration file should be created</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--fullpath</td><td></td><td>Output the full path of the migration</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--create" shortcut="" pattern="equals">
        <help><![CDATA[The table to be created]]></help>
      </option>
      <option name="--table" shortcut="" pattern="equals">
        <help><![CDATA[The table to migrate]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The location where the migration file should be created]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--fullpath" shortcut="">
        <help><![CDATA[Output the full path of the migration]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:model</name>
    <help><![CDATA[Create a new Eloquent model class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--all</td><td>(-a)</td><td>Generate a migration, seeder, factory, and resource controller for the model</td></tr> <tr><td>--controller</td><td>(-c)</td><td>Create a new controller for the model</td></tr> <tr><td>--factory</td><td>(-f)</td><td>Create a new factory for the model</td></tr> <tr><td>--force</td><td></td><td>Create the class even if the model already exists</td></tr> <tr><td>--migration</td><td>(-m)</td><td>Create a new migration file for the model</td></tr> <tr><td>--seed</td><td>(-s)</td><td>Create a new seeder file for the model</td></tr> <tr><td>--pivot</td><td>(-p)</td><td>Indicates if the generated model should be a custom intermediate table model</td></tr> <tr><td>--resource</td><td>(-r)</td><td>Indicates if the generated controller should be a resource controller</td></tr> <tr><td>--api</td><td></td><td>Indicates if the generated controller should be an API controller</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--all" shortcut="-a">
        <help><![CDATA[Generate a migration, seeder, factory, and resource controller for the model]]></help>
      </option>
      <option name="--controller" shortcut="-c">
        <help><![CDATA[Create a new controller for the model]]></help>
      </option>
      <option name="--factory" shortcut="-f">
        <help><![CDATA[Create a new factory for the model]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Create the class even if the model already exists]]></help>
      </option>
      <option name="--migration" shortcut="-m">
        <help><![CDATA[Create a new migration file for the model]]></help>
      </option>
      <option name="--seed" shortcut="-s">
        <help><![CDATA[Create a new seeder file for the model]]></help>
      </option>
      <option name="--pivot" shortcut="-p">
        <help><![CDATA[Indicates if the generated model should be a custom intermediate table model]]></help>
      </option>
      <option name="--resource" shortcut="-r">
        <help><![CDATA[Indicates if the generated controller should be a resource controller]]></help>
      </option>
      <option name="--api" shortcut="">
        <help><![CDATA[Indicates if the generated controller should be an API controller]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:notification</name>
    <help><![CDATA[Create a new notification class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td>(-f)</td><td>Create the class even if the notification already exists</td></tr> <tr><td>--markdown</td><td>(-m)</td><td>Create a new Markdown template for the notification</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--force" shortcut="-f">
        <help><![CDATA[Create the class even if the notification already exists]]></help>
      </option>
      <option name="--markdown" shortcut="-m" pattern="equals">
        <help><![CDATA[Create a new Markdown template for the notification]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:observer</name>
    <help><![CDATA[Create a new observer class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--model</td><td>(-m)</td><td>The model that the observer applies to.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--model" shortcut="-m" pattern="equals">
        <help><![CDATA[The model that the observer applies to.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:policy</name>
    <help><![CDATA[Create a new policy class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--model</td><td>(-m)</td><td>The model that the policy applies to</td></tr> <tr><td>--guard</td><td>(-g)</td><td>The guard that the policy relies on</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--model" shortcut="-m" pattern="equals">
        <help><![CDATA[The model that the policy applies to]]></help>
      </option>
      <option name="--guard" shortcut="-g" pattern="equals">
        <help><![CDATA[The guard that the policy relies on]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:provider</name>
    <help><![CDATA[Create a new service provider class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:request</name>
    <help><![CDATA[Create a new form request class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:resource</name>
    <help><![CDATA[Create a new resource<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--collection</td><td>(-c)</td><td>Create a resource collection</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--collection" shortcut="-c">
        <help><![CDATA[Create a resource collection]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:rule</name>
    <help><![CDATA[Create a new validation rule<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:seeder</name>
    <help><![CDATA[Create a new seeder class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>make:test</name>
    <help><![CDATA[Create a new test class<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--unit</td><td>(-u)</td><td>Create a unit test.</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>name</params>
    <optionsBefore>
      <option name="--unit" shortcut="-u">
        <help><![CDATA[Create a unit test.]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:fresh</name>
    <help><![CDATA[Drop all tables and re-run all migrations<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--drop-views</td><td></td><td>Drop all tables and views</td></tr> <tr><td>--drop-types</td><td></td><td>Drop all tables and types (Postgres only)</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to be executed</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--schema-path</td><td></td><td>The path to a schema dump file</td></tr> <tr><td>--seed</td><td></td><td>Indicates if the seed task should be re-run</td></tr> <tr><td>--seeder</td><td></td><td>The class name of the root seeder</td></tr> <tr><td>--step</td><td></td><td>Force the migrations to be run so they can be rolled back individually</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--drop-views" shortcut="">
        <help><![CDATA[Drop all tables and views]]></help>
      </option>
      <option name="--drop-types" shortcut="">
        <help><![CDATA[Drop all tables and types (Postgres only)]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to be executed]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--schema-path" shortcut="" pattern="equals">
        <help><![CDATA[The path to a schema dump file]]></help>
      </option>
      <option name="--seed" shortcut="">
        <help><![CDATA[Indicates if the seed task should be re-run]]></help>
      </option>
      <option name="--seeder" shortcut="" pattern="equals">
        <help><![CDATA[The class name of the root seeder]]></help>
      </option>
      <option name="--step" shortcut="">
        <help><![CDATA[Force the migrations to be run so they can be rolled back individually]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:install</name>
    <help><![CDATA[Create the migration repository<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:refresh</name>
    <help><![CDATA[Reset and re-run all migrations<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to be executed</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--seed</td><td></td><td>Indicates if the seed task should be re-run</td></tr> <tr><td>--seeder</td><td></td><td>The class name of the root seeder</td></tr> <tr><td>--step</td><td></td><td>The number of migrations to be reverted & re-run</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to be executed]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--seed" shortcut="">
        <help><![CDATA[Indicates if the seed task should be re-run]]></help>
      </option>
      <option name="--seeder" shortcut="" pattern="equals">
        <help><![CDATA[The class name of the root seeder]]></help>
      </option>
      <option name="--step" shortcut="" pattern="equals">
        <help><![CDATA[The number of migrations to be reverted & re-run]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:reset</name>
    <help><![CDATA[Rollback all database migrations<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to be executed</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--pretend</td><td></td><td>Dump the SQL queries that would be run</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to be executed]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--pretend" shortcut="">
        <help><![CDATA[Dump the SQL queries that would be run]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:rollback</name>
    <help><![CDATA[Rollback the last database migration<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to be executed</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--pretend</td><td></td><td>Dump the SQL queries that would be run</td></tr> <tr><td>--step</td><td></td><td>The number of migrations to be reverted</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to be executed]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--pretend" shortcut="">
        <help><![CDATA[Dump the SQL queries that would be run]]></help>
      </option>
      <option name="--step" shortcut="" pattern="equals">
        <help><![CDATA[The number of migrations to be reverted]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>migrate:status</name>
    <help><![CDATA[Show the status of each migration<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--path</td><td></td><td>The path(s) to the migrations files to use</td></tr> <tr><td>--realpath</td><td></td><td>Indicate any provided migration file paths are pre-resolved absolute paths</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path(s) to the migrations files to use]]></help>
      </option>
      <option name="--realpath" shortcut="">
        <help><![CDATA[Indicate any provided migration file paths are pre-resolved absolute paths]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>model:prune</name>
    <help><![CDATA[Prune models that are no longer needed<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--model</td><td></td><td>Class names of the models to be pruned</td></tr> <tr><td>--chunk</td><td></td><td>The number of models to retrieve per chunk of models to be deleted</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--model" shortcut="" pattern="equals">
        <help><![CDATA[Class names of the models to be pruned]]></help>
      </option>
      <option name="--chunk" shortcut="" pattern="equals">
        <help><![CDATA[The number of models to retrieve per chunk of models to be deleted]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>notifications:table</name>
    <help><![CDATA[Create a migration for the notifications table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>optimize:clear</name>
    <help><![CDATA[Remove the cached bootstrap files<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>package:discover</name>
    <help><![CDATA[Rebuild the cached package manifest<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>passport:client</name>
    <help><![CDATA[Create a client for issuing access tokens<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--personal</td><td></td><td>Create a personal access token client</td></tr> <tr><td>--password</td><td></td><td>Create a password grant client</td></tr> <tr><td>--client</td><td></td><td>Create a client credentials grant client</td></tr> <tr><td>--name</td><td></td><td>The name of the client</td></tr> <tr><td>--provider</td><td></td><td>The name of the user provider</td></tr> <tr><td>--redirect_uri</td><td></td><td>The URI to redirect to after authorization</td></tr> <tr><td>--user_id</td><td></td><td>The user ID the client should be assigned to</td></tr> <tr><td>--public</td><td></td><td>Create a public client (Auth code grant type only)</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--personal" shortcut="">
        <help><![CDATA[Create a personal access token client]]></help>
      </option>
      <option name="--password" shortcut="">
        <help><![CDATA[Create a password grant client]]></help>
      </option>
      <option name="--client" shortcut="">
        <help><![CDATA[Create a client credentials grant client]]></help>
      </option>
      <option name="--name" shortcut="" pattern="equals">
        <help><![CDATA[The name of the client]]></help>
      </option>
      <option name="--provider" shortcut="" pattern="equals">
        <help><![CDATA[The name of the user provider]]></help>
      </option>
      <option name="--redirect_uri" shortcut="" pattern="equals">
        <help><![CDATA[The URI to redirect to after authorization]]></help>
      </option>
      <option name="--user_id" shortcut="" pattern="equals">
        <help><![CDATA[The user ID the client should be assigned to]]></help>
      </option>
      <option name="--public" shortcut="">
        <help><![CDATA[Create a public client (Auth code grant type only)]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>passport:hash</name>
    <help><![CDATA[Hash all of the existing secrets in the clients table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td></td><td>Force the operation to run without confirmation prompt</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run without confirmation prompt]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>passport:install</name>
    <help><![CDATA[Run the commands necessary to prepare Passport for use<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--uuids</td><td></td><td>Use UUIDs for all client IDs</td></tr> <tr><td>--force</td><td></td><td>Overwrite keys they already exist</td></tr> <tr><td>--length</td><td></td><td>The length of the private key</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--uuids" shortcut="">
        <help><![CDATA[Use UUIDs for all client IDs]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite keys they already exist]]></help>
      </option>
      <option name="--length" shortcut="" pattern="equals">
        <help><![CDATA[The length of the private key]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>passport:keys</name>
    <help><![CDATA[Create the encryption keys for API authentication<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td></td><td>Overwrite keys they already exist</td></tr> <tr><td>--length</td><td></td><td>The length of the private key</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite keys they already exist]]></help>
      </option>
      <option name="--length" shortcut="" pattern="equals">
        <help><![CDATA[The length of the private key]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>passport:purge</name>
    <help><![CDATA[Purge revoked and / or expired tokens and authentication codes<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--revoked</td><td></td><td>Only purge revoked tokens and authentication codes</td></tr> <tr><td>--expired</td><td></td><td>Only purge expired tokens and authentication codes</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--revoked" shortcut="">
        <help><![CDATA[Only purge revoked tokens and authentication codes]]></help>
      </option>
      <option name="--expired" shortcut="">
        <help><![CDATA[Only purge expired tokens and authentication codes]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>prepare:installable</name>
    <help><![CDATA[Create an installable package.<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:batches-table</name>
    <help><![CDATA[Create a migration for the batches database table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:clear</name>
    <help><![CDATA[Delete all of the jobs from the specified queue<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--queue</td><td></td><td>The name of the queue to clear</td></tr> <tr><td>--force</td><td></td><td>Force the operation to run when in production</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>connection[=null]</params>
    <optionsBefore>
      <option name="--queue" shortcut="" pattern="equals">
        <help><![CDATA[The name of the queue to clear]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the operation to run when in production]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:failed</name>
    <help><![CDATA[List all of the failed queue jobs<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:failed-table</name>
    <help><![CDATA[Create a migration for the failed queue jobs database table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:flush</name>
    <help><![CDATA[Flush all of the failed queue jobs<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:forget</name>
    <help><![CDATA[Delete a failed queue job<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>id</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:listen</name>
    <help><![CDATA[Listen to a given queue<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--name</td><td></td><td>The name of the worker</td></tr> <tr><td>--delay</td><td></td><td>The number of seconds to delay failed jobs (Deprecated)</td></tr> <tr><td>--backoff</td><td></td><td>The number of seconds to wait before retrying a job that encountered an uncaught exception</td></tr> <tr><td>--force</td><td></td><td>Force the worker to run even in maintenance mode</td></tr> <tr><td>--memory</td><td></td><td>The memory limit in megabytes</td></tr> <tr><td>--queue</td><td></td><td>The queue to listen on</td></tr> <tr><td>--sleep</td><td></td><td>Number of seconds to sleep when no job is available</td></tr> <tr><td>--timeout</td><td></td><td>The number of seconds a child process can run</td></tr> <tr><td>--tries</td><td></td><td>Number of times to attempt a job before logging it failed</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>connection[=null]</params>
    <optionsBefore>
      <option name="--name" shortcut="" pattern="equals">
        <help><![CDATA[The name of the worker]]></help>
      </option>
      <option name="--delay" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds to delay failed jobs (Deprecated)]]></help>
      </option>
      <option name="--backoff" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds to wait before retrying a job that encountered an uncaught exception]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the worker to run even in maintenance mode]]></help>
      </option>
      <option name="--memory" shortcut="" pattern="equals">
        <help><![CDATA[The memory limit in megabytes]]></help>
      </option>
      <option name="--queue" shortcut="" pattern="equals">
        <help><![CDATA[The queue to listen on]]></help>
      </option>
      <option name="--sleep" shortcut="" pattern="equals">
        <help><![CDATA[Number of seconds to sleep when no job is available]]></help>
      </option>
      <option name="--timeout" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds a child process can run]]></help>
      </option>
      <option name="--tries" shortcut="" pattern="equals">
        <help><![CDATA[Number of times to attempt a job before logging it failed]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:monitor</name>
    <help><![CDATA[Monitor the size of the specified queues<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--max</td><td></td><td>The maximum number of jobs that can be on the queue before an event is dispatched</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>queues</params>
    <optionsBefore>
      <option name="--max" shortcut="" pattern="equals">
        <help><![CDATA[The maximum number of jobs that can be on the queue before an event is dispatched]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:prune-batches</name>
    <help><![CDATA[Prune stale entries from the batches database<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--hours</td><td></td><td>The number of hours to retain batch data</td></tr> <tr><td>--unfinished</td><td></td><td>The number of hours to retain unfinished batch data</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--hours" shortcut="" pattern="equals">
        <help><![CDATA[The number of hours to retain batch data]]></help>
      </option>
      <option name="--unfinished" shortcut="" pattern="equals">
        <help><![CDATA[The number of hours to retain unfinished batch data]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:prune-failed</name>
    <help><![CDATA[Prune stale entries from the failed jobs table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--hours</td><td></td><td>The number of hours to retain failed jobs data</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--hours" shortcut="" pattern="equals">
        <help><![CDATA[The number of hours to retain failed jobs data]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:restart</name>
    <help><![CDATA[Restart queue worker daemons after their current job<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:retry</name>
    <help><![CDATA[Retry a failed queue job<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--queue</td><td></td><td>Retry all of the failed jobs for the specified queue</td></tr> <tr><td>--range</td><td></td><td>Range of job IDs (numeric) to be retried</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>id[=null]</params>
    <optionsBefore>
      <option name="--queue" shortcut="" pattern="equals">
        <help><![CDATA[Retry all of the failed jobs for the specified queue]]></help>
      </option>
      <option name="--range" shortcut="" pattern="equals">
        <help><![CDATA[Range of job IDs (numeric) to be retried]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:retry-batch</name>
    <help><![CDATA[Retry the failed jobs for a batch<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>id</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:table</name>
    <help><![CDATA[Create a migration for the queue jobs database table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>queue:work</name>
    <help><![CDATA[Start processing jobs on the queue as a daemon<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--name</td><td></td><td>The name of the worker</td></tr> <tr><td>--queue</td><td></td><td>The names of the queues to work</td></tr> <tr><td>--daemon</td><td></td><td>Run the worker in daemon mode (Deprecated)</td></tr> <tr><td>--once</td><td></td><td>Only process the next job on the queue</td></tr> <tr><td>--stop-when-empty</td><td></td><td>Stop when the queue is empty</td></tr> <tr><td>--delay</td><td></td><td>The number of seconds to delay failed jobs (Deprecated)</td></tr> <tr><td>--backoff</td><td></td><td>The number of seconds to wait before retrying a job that encountered an uncaught exception</td></tr> <tr><td>--max-jobs</td><td></td><td>The number of jobs to process before stopping</td></tr> <tr><td>--max-time</td><td></td><td>The maximum number of seconds the worker should run</td></tr> <tr><td>--force</td><td></td><td>Force the worker to run even in maintenance mode</td></tr> <tr><td>--memory</td><td></td><td>The memory limit in megabytes</td></tr> <tr><td>--sleep</td><td></td><td>Number of seconds to sleep when no job is available</td></tr> <tr><td>--rest</td><td></td><td>Number of seconds to rest between jobs</td></tr> <tr><td>--timeout</td><td></td><td>The number of seconds a child process can run</td></tr> <tr><td>--tries</td><td></td><td>Number of times to attempt a job before logging it failed</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>connection[=null]</params>
    <optionsBefore>
      <option name="--name" shortcut="" pattern="equals">
        <help><![CDATA[The name of the worker]]></help>
      </option>
      <option name="--queue" shortcut="" pattern="equals">
        <help><![CDATA[The names of the queues to work]]></help>
      </option>
      <option name="--daemon" shortcut="">
        <help><![CDATA[Run the worker in daemon mode (Deprecated)]]></help>
      </option>
      <option name="--once" shortcut="">
        <help><![CDATA[Only process the next job on the queue]]></help>
      </option>
      <option name="--stop-when-empty" shortcut="">
        <help><![CDATA[Stop when the queue is empty]]></help>
      </option>
      <option name="--delay" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds to delay failed jobs (Deprecated)]]></help>
      </option>
      <option name="--backoff" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds to wait before retrying a job that encountered an uncaught exception]]></help>
      </option>
      <option name="--max-jobs" shortcut="" pattern="equals">
        <help><![CDATA[The number of jobs to process before stopping]]></help>
      </option>
      <option name="--max-time" shortcut="" pattern="equals">
        <help><![CDATA[The maximum number of seconds the worker should run]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Force the worker to run even in maintenance mode]]></help>
      </option>
      <option name="--memory" shortcut="" pattern="equals">
        <help><![CDATA[The memory limit in megabytes]]></help>
      </option>
      <option name="--sleep" shortcut="" pattern="equals">
        <help><![CDATA[Number of seconds to sleep when no job is available]]></help>
      </option>
      <option name="--rest" shortcut="" pattern="equals">
        <help><![CDATA[Number of seconds to rest between jobs]]></help>
      </option>
      <option name="--timeout" shortcut="" pattern="equals">
        <help><![CDATA[The number of seconds a child process can run]]></help>
      </option>
      <option name="--tries" shortcut="" pattern="equals">
        <help><![CDATA[Number of times to attempt a job before logging it failed]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>route:cache</name>
    <help><![CDATA[Create a route cache file for faster route registration<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>route:clear</name>
    <help><![CDATA[Remove the route cache file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>route:list</name>
    <help><![CDATA[List all registered routes<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--columns</td><td></td><td>Columns to include in the route table</td></tr> <tr><td>--compact</td><td>(-c)</td><td>Only show method, URI and action columns</td></tr> <tr><td>--json</td><td></td><td>Output the route list as JSON</td></tr> <tr><td>--method</td><td></td><td>Filter the routes by method</td></tr> <tr><td>--name</td><td></td><td>Filter the routes by name</td></tr> <tr><td>--path</td><td></td><td>Only show routes matching the given path pattern</td></tr> <tr><td>--except-path</td><td></td><td>Do not display the routes matching the given path pattern</td></tr> <tr><td>--reverse</td><td>(-r)</td><td>Reverse the ordering of the routes</td></tr> <tr><td>--sort</td><td></td><td>The column (precedence, domain, method, uri, name, action, middleware) to sort by</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--columns" shortcut="" pattern="equals">
        <help><![CDATA[Columns to include in the route table]]></help>
      </option>
      <option name="--compact" shortcut="-c">
        <help><![CDATA[Only show method, URI and action columns]]></help>
      </option>
      <option name="--json" shortcut="">
        <help><![CDATA[Output the route list as JSON]]></help>
      </option>
      <option name="--method" shortcut="" pattern="equals">
        <help><![CDATA[Filter the routes by method]]></help>
      </option>
      <option name="--name" shortcut="" pattern="equals">
        <help><![CDATA[Filter the routes by name]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[Only show routes matching the given path pattern]]></help>
      </option>
      <option name="--except-path" shortcut="" pattern="equals">
        <help><![CDATA[Do not display the routes matching the given path pattern]]></help>
      </option>
      <option name="--reverse" shortcut="-r">
        <help><![CDATA[Reverse the ordering of the routes]]></help>
      </option>
      <option name="--sort" shortcut="" pattern="equals">
        <help><![CDATA[The column (precedence, domain, method, uri, name, action, middleware) to sort by]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>sail:install</name>
    <help><![CDATA[Install Laravel Sail's default Docker Compose file<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--with</td><td></td><td>The services that should be included in the installation</td></tr> <tr><td>--devcontainer</td><td></td><td>Create a .devcontainer configuration directory</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--with" shortcut="" pattern="equals">
        <help><![CDATA[The services that should be included in the installation]]></help>
      </option>
      <option name="--devcontainer" shortcut="">
        <help><![CDATA[Create a .devcontainer configuration directory]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>sail:publish</name>
    <help><![CDATA[Publish the Laravel Sail Docker files<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:finish</name>
    <help><![CDATA[Handle the completion of a scheduled command<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <params>id code[=null]</params>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:list</name>
    <help><![CDATA[List the scheduled commands<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--timezone</td><td></td><td>The timezone that times should be displayed in</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--timezone" shortcut="" pattern="equals">
        <help><![CDATA[The timezone that times should be displayed in]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:run</name>
    <help><![CDATA[Run the scheduled commands<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:test</name>
    <help><![CDATA[Run a scheduled command<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schedule:work</name>
    <help><![CDATA[Start the schedule worker<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>schema:dump</name>
    <help><![CDATA[Dump the given database schema<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--database</td><td></td><td>The database connection to use</td></tr> <tr><td>--path</td><td></td><td>The path where the schema dump file should be stored</td></tr> <tr><td>--prune</td><td></td><td>Delete all existing migration files</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--database" shortcut="" pattern="equals">
        <help><![CDATA[The database connection to use]]></help>
      </option>
      <option name="--path" shortcut="" pattern="equals">
        <help><![CDATA[The path where the schema dump file should be stored]]></help>
      </option>
      <option name="--prune" shortcut="">
        <help><![CDATA[Delete all existing migration files]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>session:table</name>
    <help><![CDATA[Create a migration for the session database table<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>storage:link</name>
    <help><![CDATA[Create the symbolic links configured for the application<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--relative</td><td></td><td>Create the symbolic link using relative paths</td></tr> <tr><td>--force</td><td></td><td>Recreate existing symbolic links</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--relative" shortcut="">
        <help><![CDATA[Create the symbolic link using relative paths]]></help>
      </option>
      <option name="--force" shortcut="">
        <help><![CDATA[Recreate existing symbolic links]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>stub:publish</name>
    <help><![CDATA[Publish all stubs that are available for customization<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td></td><td>Overwrite any existing files</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite any existing files]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>vendor:publish</name>
    <help><![CDATA[Publish any publishable assets from vendor packages<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--force</td><td></td><td>Overwrite any existing files</td></tr> <tr><td>--all</td><td></td><td>Publish assets for all service providers without prompt</td></tr> <tr><td>--provider</td><td></td><td>The service provider that has assets you want to publish</td></tr> <tr><td>--tag</td><td></td><td>One or many tags that have assets you want to publish</td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--force" shortcut="">
        <help><![CDATA[Overwrite any existing files]]></help>
      </option>
      <option name="--all" shortcut="">
        <help><![CDATA[Publish assets for all service providers without prompt]]></help>
      </option>
      <option name="--provider" shortcut="" pattern="equals">
        <help><![CDATA[The service provider that has assets you want to publish]]></help>
      </option>
      <option name="--tag" shortcut="" pattern="equals">
        <help><![CDATA[One or many tags that have assets you want to publish]]></help>
      </option>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>view:cache</name>
    <help><![CDATA[Compile all of the application's Blade templates<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
  <command>
    <name>view:clear</name>
    <help><![CDATA[Clear all compiled view files<br/><br/><table> <tr><td><strong>Options:</strong></td></tr> <tr><td>--help</td><td>(-h)</td><td>Display help for the given command. When no command is given display help for the <b>list</b> command</td></tr> <tr><td>--quiet</td><td>(-q)</td><td>Do not output any message</td></tr> <tr><td>--verbose</td><td>(-v)</td><td>Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug</td></tr> <tr><td>--version</td><td>(-V)</td><td>Display this application version</td></tr> <tr><td>--ansi</td><td></td><td>Force (or disable --no-ansi) ANSI output</td></tr> <tr><td>--no-ansi</td><td></td><td>Negate the "--ansi" option</td></tr> <tr><td>--no-interaction</td><td>(-n)</td><td>Do not ask any interactive question</td></tr> <tr><td>--env</td><td></td><td>The environment the command should run under</td></tr> </table> <br/>]]></help>
    <optionsBefore>
      <option name="--help" shortcut="-h">
        <help><![CDATA[Display help for the given command. When no command is given display help for the <b>list</b> command]]></help>
      </option>
      <option name="--quiet" shortcut="-q">
        <help><![CDATA[Do not output any message]]></help>
      </option>
      <option name="--verbose" shortcut="-v">
        <help><![CDATA[Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug]]></help>
      </option>
      <option name="--version" shortcut="-V">
        <help><![CDATA[Display this application version]]></help>
      </option>
      <option name="--ansi" shortcut="">
        <help><![CDATA[Force (or disable --no-ansi) ANSI output]]></help>
      </option>
      <option name="--no-ansi" shortcut="">
        <help><![CDATA[Negate the "--ansi" option]]></help>
      </option>
      <option name="--no-interaction" shortcut="-n">
        <help><![CDATA[Do not ask any interactive question]]></help>
      </option>
      <option name="--env" shortcut="" pattern="equals">
        <help><![CDATA[The environment the command should run under]]></help>
      </option>
    </optionsBefore>
  </command>
</framework>

