<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/vendor/unicodeveloper/laravel-paystack" />
      <path value="$PROJECT_DIR$/vendor/mercadopago/dx-php" />
      <path value="$PROJECT_DIR$/vendor/matanyadaev/laravel-eloquent-spatial" />
      <path value="$PROJECT_DIR$/vendor/intervention/image" />
      <path value="$PROJECT_DIR$/vendor/rap2hpoutre/fast-excel" />
      <path value="$PROJECT_DIR$/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/vendor/laravelpkg/laravelchk" />
      <path value="$PROJECT_DIR$/vendor/maatwebsite/excel" />
      <path value="$PROJECT_DIR$/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/vendor/beyondcode/laravel-websockets" />
      <path value="$PROJECT_DIR$/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/vendor/paragonie/sodium_compat" />
      <path value="$PROJECT_DIR$/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/vendor/openspout/openspout" />
      <path value="$PROJECT_DIR$/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/vendor/evenement/evenement" />
      <path value="$PROJECT_DIR$/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/vendor/razorpay/razorpay" />
      <path value="$PROJECT_DIR$/vendor/maximebf/debugbar" />
      <path value="$PROJECT_DIR$/vendor/brian2694/laravel-toastr" />
      <path value="$PROJECT_DIR$/vendor/setasign/fpdi" />
      <path value="$PROJECT_DIR$/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/vendor/firebase/php-jwt" />
      <path value="$PROJECT_DIR$/vendor/lcobucci/clock" />
      <path value="$PROJECT_DIR$/vendor/lcobucci/jwt" />
      <path value="$PROJECT_DIR$/vendor/doctrine/cache" />
      <path value="$PROJECT_DIR$/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/vendor/doctrine/event-manager" />
      <path value="$PROJECT_DIR$/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/vendor/barryvdh/laravel-debugbar" />
      <path value="$PROJECT_DIR$/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/vendor/grpc/grpc" />
      <path value="$PROJECT_DIR$/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/fig/http-message-util" />
      <path value="$PROJECT_DIR$/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/vendor/ratchet/rfc6455" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/vendor/nwidart/laravel-modules" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/vendor/madnest/madzipper" />
      <path value="$PROJECT_DIR$/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/laravel/passport" />
      <path value="$PROJECT_DIR$/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/vendor/xendit/xendit-php" />
      <path value="$PROJECT_DIR$/vendor/gregwar/captcha" />
      <path value="$PROJECT_DIR$/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/vendor/stripe/stripe-php" />
      <path value="$PROJECT_DIR$/vendor/spatie/backtrace" />
      <path value="$PROJECT_DIR$/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/vendor/twilio/sdk" />
      <path value="$PROJECT_DIR$/vendor/spatie/laravel-ignition" />
      <path value="$PROJECT_DIR$/vendor/spatie/flare-client-php" />
      <path value="$PROJECT_DIR$/vendor/spatie/error-solutions" />
      <path value="$PROJECT_DIR$/vendor/spatie/ignition" />
      <path value="$PROJECT_DIR$/vendor/rmccue/requests" />
      <path value="$PROJECT_DIR$/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/vendor/pusher/pusher-php-server" />
      <path value="$PROJECT_DIR$/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/vendor/paypal/paypal-checkout-sdk" />
      <path value="$PROJECT_DIR$/vendor/nyholm/psr7" />
      <path value="$PROJECT_DIR$/vendor/phayes/geophp" />
      <path value="$PROJECT_DIR$/vendor/paypal/paypalhttp" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/vendor/league/event" />
      <path value="$PROJECT_DIR$/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/vendor/league/uri-interfaces" />
      <path value="$PROJECT_DIR$/vendor/league/oauth2-server" />
      <path value="$PROJECT_DIR$/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/vendor/league/config" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem-aws-s3-v3" />
      <path value="$PROJECT_DIR$/vendor/league/uri" />
      <path value="$PROJECT_DIR$/vendor/iyzico/iyzipay-php" />
      <path value="$PROJECT_DIR$/vendor/google/cloud-storage" />
      <path value="$PROJECT_DIR$/vendor/kreait/firebase-php" />
      <path value="$PROJECT_DIR$/vendor/kreait/firebase-tokens" />
      <path value="$PROJECT_DIR$/vendor/google/longrunning" />
      <path value="$PROJECT_DIR$/vendor/google/gax" />
      <path value="$PROJECT_DIR$/vendor/google/common-protos" />
      <path value="$PROJECT_DIR$/vendor/google/auth" />
      <path value="$PROJECT_DIR$/vendor/google/grpc-gcp" />
      <path value="$PROJECT_DIR$/vendor/google/cloud-core" />
      <path value="$PROJECT_DIR$/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/vendor/defuse/php-encryption" />
      <path value="$PROJECT_DIR$/vendor/google/protobuf" />
      <path value="$PROJECT_DIR$/vendor/facade/ignition-contracts" />
      <path value="$PROJECT_DIR$/vendor/react/cache" />
      <path value="$PROJECT_DIR$/vendor/react/dns" />
      <path value="$PROJECT_DIR$/vendor/cboden/ratchet" />
      <path value="$PROJECT_DIR$/vendor/react/stream" />
      <path value="$PROJECT_DIR$/vendor/react/socket" />
      <path value="$PROJECT_DIR$/vendor/react/http" />
      <path value="$PROJECT_DIR$/vendor/react/event-loop" />
      <path value="$PROJECT_DIR$/vendor/react/promise" />
      <path value="$PROJECT_DIR$/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/vendor/brick/math" />
      <path value="$PROJECT_DIR$/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/vendor/beste/in-memory-cache" />
      <path value="$PROJECT_DIR$/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/vendor/beste/clock" />
      <path value="$PROJECT_DIR$/vendor/beste/json" />
      <path value="$PROJECT_DIR$/vendor/mpdf/psr-http-message-shim" />
      <path value="$PROJECT_DIR$/vendor/mpdf/mpdf" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/rize/uri-template" />
      <path value="$PROJECT_DIR$/vendor/mpdf/psr-log-aware-trait" />
      <path value="$PROJECT_DIR$/vendor/doctrine/instantiator" />
      <path value="$PROJECT_DIR$/vendor/swiftmailer/swiftmailer" />
      <path value="$PROJECT_DIR$/vendor/fideloper/proxy" />
      <path value="$PROJECT_DIR$/vendor/fruitcake/laravel-cors" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-iconv" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php73" />
      <path value="$PROJECT_DIR$/vendor/asm89/stack-cors" />
      <path value="$PROJECT_DIR$/vendor/sebastian/resource-operations" />
      <path value="$PROJECT_DIR$/vendor/phpspec/prophecy" />
      <path value="$PROJECT_DIR$/vendor/opis/closure" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/vendor/facade/ignition" />
      <path value="$PROJECT_DIR$/vendor/facade/flare-client-php" />
      <path value="$PROJECT_DIR$/vendor/jmikola/geojson" />
      <path value="$PROJECT_DIR$/vendor/grimzy/laravel-mysql-spatial" />
      <path value="$PROJECT_DIR$/vendor/geo-io/interface" />
      <path value="$PROJECT_DIR$/vendor/geo-io/wkb-parser" />
      <path value="$PROJECT_DIR$/vendor/symfony/debug" />
      <path value="$PROJECT_DIR$/vendor/paypal/rest-api-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/php-http/message-factory" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php81" />
      <path value="$PROJECT_DIR$/vendor/vonage/client-core" />
      <path value="$PROJECT_DIR$/vendor/vonage/client" />
      <path value="$PROJECT_DIR$/vendor/vonage/nexmo-bridge" />
      <path value="$PROJECT_DIR$/vendor/nexmo/laravel" />
      <path value="$PROJECT_DIR$/vendor/laminas/laminas-diactoros" />
      <path value="$PROJECT_DIR$/vendor/box/spout" />
      <path value="$PROJECT_DIR$/vendor/barryvdh/laravel-cors" />
      <path value="$PROJECT_DIR$/vendor/doctrine/annotations" />
      <path value="$PROJECT_DIR$/vendor/doctrine/persistence" />
      <path value="$PROJECT_DIR$/vendor/doctrine/common" />
      <path value="$PROJECT_DIR$/vendor/doctrine/collections" />
      <path value="$PROJECT_DIR$/vendor/kingflamez/laravelrave" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client-contracts" />
      <path value="$PROJECT_DIR$/vendor/laminas/laminas-zendframework-bridge" />
      <path value="$PROJECT_DIR$/vendor/mollie/mollie-api-php" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/vendor/ringcentral/psr7" />
      <path value="$PROJECT_DIR$/vendor/vendor/guzzlehttp" />
      <path value="$PROJECT_DIR$/vendor/vendor/xendit" />
      <path value="$PROJECT_DIR$/vendor/vendor/madnest" />
      <path value="$PROJECT_DIR$/vendor/vendor/maatwebsite" />
      <path value="$PROJECT_DIR$/vendor/vendor/ramsey" />
      <path value="$PROJECT_DIR$/vendor/vendor/paypal" />
      <path value="$PROJECT_DIR$/vendor/vendor/firebase" />
      <path value="$PROJECT_DIR$/vendor/vendor/fig" />
      <path value="$PROJECT_DIR$/vendor/vendor/grpc" />
      <path value="$PROJECT_DIR$/vendor/vendor/paragonie" />
      <path value="$PROJECT_DIR$/vendor/vendor/nwidart" />
      <path value="$PROJECT_DIR$/vendor/vendor/gregwar" />
      <path value="$PROJECT_DIR$/vendor/vendor/hamcrest" />
      <path value="$PROJECT_DIR$/vendor/vendor/brick" />
      <path value="$PROJECT_DIR$/vendor/vendor/ringcentral" />
      <path value="$PROJECT_DIR$/vendor/vendor/phpoption" />
      <path value="$PROJECT_DIR$/vendor/vendor/kreait" />
      <path value="$PROJECT_DIR$/vendor/vendor/webmozart" />
      <path value="$PROJECT_DIR$/vendor/vendor/nesbot" />
      <path value="$PROJECT_DIR$/vendor/vendor/beyondcode" />
      <path value="$PROJECT_DIR$/vendor/vendor/tijsverkoyen" />
      <path value="$PROJECT_DIR$/vendor/vendor/maennchen" />
      <path value="$PROJECT_DIR$/vendor/vendor/react" />
      <path value="$PROJECT_DIR$/vendor/vendor/phpoffice" />
      <path value="$PROJECT_DIR$/vendor/vendor/google" />
      <path value="$PROJECT_DIR$/vendor/vendor/mercadopago" />
      <path value="$PROJECT_DIR$/vendor/vendor/evenement" />
      <path value="$PROJECT_DIR$/vendor/vendor/spatie" />
      <path value="$PROJECT_DIR$/vendor/vendor/laravel" />
      <path value="$PROJECT_DIR$/vendor/vendor/symfony" />
      <path value="$PROJECT_DIR$/vendor/vendor/rmccue" />
      <path value="$PROJECT_DIR$/vendor/vendor/myclabs" />
      <path value="$PROJECT_DIR$/vendor/vendor/psr" />
      <path value="$PROJECT_DIR$/vendor/vendor/rize" />
      <path value="$PROJECT_DIR$/vendor/vendor/mockery" />
      <path value="$PROJECT_DIR$/vendor/vendor/lcobucci" />
      <path value="$PROJECT_DIR$/vendor/vendor/ratchet" />
      <path value="$PROJECT_DIR$/vendor/vendor/brian2694" />
      <path value="$PROJECT_DIR$/vendor/vendor/graham-campbell" />
      <path value="$PROJECT_DIR$/vendor/vendor/psy" />
      <path value="$PROJECT_DIR$/vendor/vendor/nikic" />
      <path value="$PROJECT_DIR$/vendor/vendor/fakerphp" />
      <path value="$PROJECT_DIR$/vendor/vendor/rap2hpoutre" />
      <path value="$PROJECT_DIR$/vendor/vendor/dragonmantank" />
      <path value="$PROJECT_DIR$/vendor/vendor/nyholm" />
      <path value="$PROJECT_DIR$/vendor/vendor/theseer" />
      <path value="$PROJECT_DIR$/vendor/vendor/phpseclib" />
      <path value="$PROJECT_DIR$/vendor/vendor/defuse" />
      <path value="$PROJECT_DIR$/vendor/vendor/facade" />
      <path value="$PROJECT_DIR$/vendor/vendor/phar-io" />
      <path value="$PROJECT_DIR$/vendor/vendor/maximebf" />
      <path value="$PROJECT_DIR$/vendor/vendor/dflydev" />
      <path value="$PROJECT_DIR$/vendor/vendor/aws" />
      <path value="$PROJECT_DIR$/vendor/vendor/phpunit" />
      <path value="$PROJECT_DIR$/vendor/vendor/unicodeveloper" />
      <path value="$PROJECT_DIR$/vendor/vendor/egulias" />
      <path value="$PROJECT_DIR$/vendor/vendor/setasign" />
      <path value="$PROJECT_DIR$/vendor/vendor/cboden" />
      <path value="$PROJECT_DIR$/vendor/vendor/nunomaduro" />
      <path value="$PROJECT_DIR$/vendor/vendor/nette" />
      <path value="$PROJECT_DIR$/vendor/vendor/matanyadaev" />
      <path value="$PROJECT_DIR$/vendor/__MACOSX/vendor" />
      <path value="$PROJECT_DIR$/vendor/vendor/bin" />
      <path value="$PROJECT_DIR$/vendor/vendor/barryvdh" />
      <path value="$PROJECT_DIR$/vendor/vendor/laravelpkg" />
      <path value="$PROJECT_DIR$/vendor/vendor/pusher" />
      <path value="$PROJECT_DIR$/vendor/vendor/ezyang" />
      <path value="$PROJECT_DIR$/vendor/vendor/iyzico" />
      <path value="$PROJECT_DIR$/vendor/vendor/monolog" />
      <path value="$PROJECT_DIR$/vendor/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/vendor/voku" />
      <path value="$PROJECT_DIR$/vendor/vendor/openspout" />
      <path value="$PROJECT_DIR$/vendor/vendor/ralouphie" />
      <path value="$PROJECT_DIR$/vendor/vendor/razorpay" />
      <path value="$PROJECT_DIR$/vendor/vendor/phayes" />
      <path value="$PROJECT_DIR$/vendor/vendor/sebastian" />
      <path value="$PROJECT_DIR$/vendor/vendor/doctrine" />
      <path value="$PROJECT_DIR$/vendor/vendor/beste" />
      <path value="$PROJECT_DIR$/vendor/vendor/markbaker" />
      <path value="$PROJECT_DIR$/vendor/vendor/twilio" />
      <path value="$PROJECT_DIR$/vendor/vendor/stripe" />
      <path value="$PROJECT_DIR$/vendor/vendor/mpdf" />
      <path value="$PROJECT_DIR$/vendor/vendor/league" />
      <path value="$PROJECT_DIR$/vendor/vendor/fruitcake" />
      <path value="$PROJECT_DIR$/vendor/vendor/filp" />
      <path value="$PROJECT_DIR$/vendor/vendor/mtdowling" />
      <path value="$PROJECT_DIR$/vendor/vendor/vlucas" />
      <path value="$PROJECT_DIR$/vendor/vendor/intervention" />
      <path value="$PROJECT_DIR$/vendor/vendor/dompdf" />
      <path value="$PROJECT_DIR$/vendor/vendor/bacon" />
      <path value="$PROJECT_DIR$/vendor/vendor/staudenmeir" />
      <path value="$PROJECT_DIR$/vendor/vendor/sabberworm" />
      <path value="$PROJECT_DIR$/vendor/vendor/dasprid" />
      <path value="$PROJECT_DIR$/vendor/vendor/masterminds" />
      <path value="$PROJECT_DIR$/vendor/vendor/phenx" />
      <path value="$PROJECT_DIR$/vendor/vendor/simplesoftwareio" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.2">
    <option name="suggestChangeDefaultLanguageLevel" value="false" />
  </component>
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PhpUnit">
    <phpunit_settings>
      <PhpUnitSettings configuration_file_path="$PROJECT_DIR$/phpunit.xml" custom_loader_path="$PROJECT_DIR$/vendor/autoload.php" use_configuration_file="true" />
    </phpunit_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>