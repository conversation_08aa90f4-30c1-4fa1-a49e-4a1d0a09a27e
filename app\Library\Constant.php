<?php

//payment methods
const GATEWAYS_PAYMENT_METHODS = [
    ['key' => 'ssl_commerz', 'value' => 'SSLCOMMERZ'],
    ['key' => 'stripe', 'value' => 'Stripe'],
    ['key' => 'paypal', 'value' => 'PayPal'],
    ['key' => 'razor_pay', 'value' => 'Razor Pay'],
    ['key' => 'paystack', 'value' => 'Paystack'],
    ['key' => 'senang_pay', 'value' => 'Senang Pay'],
    ['key' => 'paymob_accept', 'value' => 'Paymob Accept'],
    ['key' => 'flutterwave', 'value' => 'Flutter Wave'],
    ['key' => 'paytm', 'value' => 'Paytm'],
    ['key' => 'paytabs', 'value' => 'Pay Tabs'],
    ['key' => 'liqpay', 'value' => 'Liq Pay'],
    ['key' => 'mercadopago', 'value' => 'Mercadopago'],
    ['key' => 'bkash', 'value' => 'Bkash'],
    ['key' => 'fatoorah', 'value' => 'Fatoorah'],
    ['key' => 'xendit', 'value' => 'Xendit'],
    ['key' => 'amazon_pay', 'value' => 'Amazon Pay'],
    ['key' => 'iyzi_pay', 'value' => 'Iyzi Pay'],
    ['key' => 'hyper_pay', 'value' => 'Hyper Pay'],
    ['key' => 'foloosi', 'value' => 'Foloosi'],
    ['key' => 'ccavenue', 'value' => 'CC Avenue'],
    ['key' => 'pvit', 'value' => 'Pvit'],
    ['key' => 'moncash', 'value' => 'Moncash'],
    ['key' => 'thawani', 'value' => 'Thawani'],
    ['key' => 'tap', 'value' => 'Tap Payment'],
    ['key' => 'viva_wallet', 'value' => 'Viva Wallet'],
    ['key' => 'hubtel', 'value' => 'Hubtel'],
    ['key' => 'maxicash', 'value' => 'Maxicash'],
    ['key' => 'esewa', 'value' => 'Esewa'],
    ['key' => 'swish', 'value' => 'Swish'],
    ['key' => 'momo', 'value' => 'Momo'],
    ['key' => 'payfast', 'value' => 'Pay Fast'],
    ['key' => 'worldpay', 'value' => 'World Pay'],
    ['key' => 'sixcash', 'value' => 'Six Cash'],
];

//currencies
const GATEWAYS_CURRENCIES = [
    ["code" => "AED", "symbol" => "د.إ", "name" => "UAE dirham"],
    ["code" => "AFN", "symbol" => "Afs", "name" => "Afghan afghani"],
    ["code" => "ALL", "symbol" => "L", "name" => "Albanian lek"],
    ["code" => "AMD", "symbol" => "AMD", "name" => "Armenian dram"],
    ["code" => "ANG", "symbol" => "NAƒ", "name" => "Netherlands Antillean gulden"],
    ["code" => "AOA", "symbol" => "Kz", "name" => "Angolan kwanza"],
    ["code" => "ARS", "symbol" => "$", "name" => "Argentine peso"],
    ["code" => "AUD", "symbol" => "$", "name" => "Australian dollar"],
    ["code" => "AWG", "symbol" => "ƒ", "name" => "Aruban florin"],
    ["code" => "AZN", "symbol" => "AZN", "name" => "Azerbaijani manat"],
    ["code" => "BAM", "symbol" => "KM", "name" => "Bosnia and Herzegovina konvertibilna marka"],
    ["code" => "BBD", "symbol" => "Bds$", "name" => "Barbadian dollar"],
    ["code" => "BDT", "symbol" => "৳", "name" => "Bangladeshi taka"],
    ["code" => "BGN", "symbol" => "BGN", "name" => "Bulgarian lev"],
    ["code" => "BHD", "symbol" => ".د.ب", "name" => "Bahraini dinar"],
    ["code" => "BIF", "symbol" => "FBu", "name" => "Burundi franc"],
    ["code" => "BMD", "symbol" => "BD$", "name" => "Bermudian dollar"],
    ["code" => "BND", "symbol" => "B$", "name" => "Brunei dollar"],
    ["code" => "BOB", "symbol" => "Bs.", "name" => "Bolivian boliviano"],
    ["code" => "BRL", "symbol" => "R$", "name" => "Brazilian real"],
    ["code" => "BSD", "symbol" => "B$", "name" => "Bahamian dollar"],
    ["code" => "BTN", "symbol" => "Nu.", "name" => "Bhutanese ngultrum"],
    ["code" => "BWP", "symbol" => "P", "name" => "Botswana pula"],
    ["code" => "BYR", "symbol" => "Br", "name" => "Belarusian ruble"],
    ["code" => "BZD", "symbol" => "BZ$", "name" => "Belize dollar"],
    ["code" => "CAD", "symbol" => "$", "name" => "Canadian dollar"],
    ["code" => "CDF", "symbol" => "F", "name" => "Congolese franc"],
    ["code" => "CHF", "symbol" => "Fr.", "name" => "Swiss franc"],
    ["code" => "CLP", "symbol" => "$", "name" => "Chilean peso"],
    ["code" => "CNY", "symbol" => "¥", "name" => "Chinese/Yuan renminbi"],
    ["code" => "COP", "symbol" => "Col$", "name" => "Colombian peso"],
    ["code" => "CRC", "symbol" => "₡", "name" => "Costa Rican colon"],
    ["code" => "CUC", "symbol" => "$", "name" => "Cuban peso"],
    ["code" => "CVE", "symbol" => "Esc", "name" => "Cape Verdean escudo"],
    ["code" => "CZK", "symbol" => "Kč", "name" => "Czech koruna"],
    ["code" => "DJF", "symbol" => "Fdj", "name" => "Djiboutian franc"],
    ["code" => "DKK", "symbol" => "Kr", "name" => "Danish krone"],
    ["code" => "DOP", "symbol" => "RD$", "name" => "Dominican peso"],
    ["code" => "DZD", "symbol" => "دج", "name" => "Algerian dinar"],
    ["code" => "EEK", "symbol" => "KR", "name" => "Estonian kroon"],
    ["code" => "EGP", "symbol" => "e£", "name" => "Egyptian pound"],
    ["code" => "ERN", "symbol" => "Nfa", "name" => "Eritrean nakfa"],
    ["code" => "ETB", "symbol" => "Br", "name" => "Ethiopian birr"],
    ["code" => "EUR", "symbol" => "€", "name" => "European Euro"],
    ["code" => "FJD", "symbol" => "FJ$", "name" => "Fijian dollar"],
    ["code" => "FKP", "symbol" => "£", "name" => "Falkland Islands pound"],
    ["code" => "GBP", "symbol" => "£", "name" => "British pound"],
    ["code" => "GEL", "symbol" => "GEL", "name" => "Georgian lari"],
    ["code" => "GHS", "symbol" => "GH¢", "name" => "Ghanaian cedi"],
    ["code" => "GIP", "symbol" => "£", "name" => "Gibraltar pound"],
    ["code" => "GMD", "symbol" => "D", "name" => "Gambian dalasi"],
    ["code" => "GNF", "symbol" => "FG", "name" => "Guinean franc"],
    ["code" => "GQE", "symbol" => "CFA", "name" => "Central African CFA franc"],
    ["code" => "GTQ", "symbol" => "Q", "name" => "Guatemalan quetzal"],
    ["code" => "GYD", "symbol" => "GY$", "name" => "Guyanese dollar"],
    ["code" => "HKD", "symbol" => "HK$", "name" => "Hong Kong dollar"],
    ["code" => "HNL", "symbol" => "L", "name" => "Honduran lempira"],
    ["code" => "HRK", "symbol" => "kn", "name" => "Croatian kuna"],
    ["code" => "HTG", "symbol" => "G", "name" => "Haitian gourde"],
    ["code" => "HUF", "symbol" => "Ft", "name" => "Hungarian forint"],
    ["code" => "IDR", "symbol" => "Rp", "name" => "Indonesian rupiah"],
    ["code" => "ILS", "symbol" => "₪", "name" => "Israeli new sheqel"],
    ["code" => "INR", "symbol" => "₹", "name" => "Indian rupee"],
    ["code" => "IQD", "symbol" => "ع.د", "name" => "Iraqi dinar"],
    ["code" => "IRR", "symbol" => "IRR", "name" => "Iranian rial"],
    ["code" => "ISK", "symbol" => "kr", "name" => "Icelandic kr\u00f3na"],
    ["code" => "JMD", "symbol" => "J$", "name" => "Jamaican dollar"],
    ["code" => "JOD", "symbol" => "JOD", "name" => "Jordanian dinar"],
    ["code" => "JPY", "symbol" => "¥", "name" => "Japanese yen"],
    ["code" => "KES", "symbol" => "KSh", "name" => "Kenyan shilling"],
    ["code" => "KGS", "symbol" => "Лв", "name" => "Kyrgyzstani som"],
    ["code" => "KHR", "symbol" => "៛", "name" => "Cambodian riel"],
    ["code" => "KMF", "symbol" => "KMF", "name" => "Comorian franc"],
    ["code" => "KPW", "symbol" => "W", "name" => "North Korean won"],
    ["code" => "KRW", "symbol" => "W", "name" => "South Korean won"],
    ["code" => "KWD", "symbol" => "KWD", "name" => "Kuwaiti dinar"],
    ["code" => "KYD", "symbol" => "KY$", "name" => "Cayman Islands dollar"],
    ["code" => "KZT", "symbol" => "T", "name" => "Kazakhstani tenge"],
    ["code" => "LAK", "symbol" => "KN", "name" => "Lao kip"],
    ["code" => "LBP", "symbol" => ".ل.ل", "name" => "Lebanese lira"],
    ["code" => "LKR", "symbol" => "Rs", "name" => "Sri Lankan rupee"],
    ["code" => "LRD", "symbol" => "L$", "name" => "Liberian dollar"],
    ["code" => "LSL", "symbol" => "M", "name" => "Lesotho loti"],
    ["code" => "LTL", "symbol" => "Lt", "name" => "Lithuanian litas"],
    ["code" => "LVL", "symbol" => "Ls", "name" => "Latvian lats"],
    ["code" => "LYD", "symbol" => "LD", "name" => "Libyan dinar"],
    ["code" => "MAD", "symbol" => "MAD", "name" => "Morocodean dirham"],
    ["code" => "MDL", "symbol" => "MDL", "name" => "Moldovan leu"],
    ["code" => "MGA", "symbol" => "FMG", "name" => "Malagasy ariary"],
    ["code" => "MKD", "symbol" => "MKD", "name" => "Macedonian denar"],
    ["code" => "MMK", "symbol" => "K", "name" => "Myanma kyat"],
    ["code" => "MNT", "symbol" => "₮", "name" => "Mongolian tugrik"],
    ["code" => "MOP", "symbol" => "P", "name" => "Macanese pataca"],
    ["code" => "MRO", "symbol" => "UM", "name" => "Mauritanian ouguiya"],
    ["code" => "MUR", "symbol" => "Rs", "name" => "Mauritian rupee"],
    ["code" => "MVR", "symbol" => "Rf", "name" => "Maldivian rufiyaa"],
    ["code" => "MWK", "symbol" => "MK", "name" => "Malawian kwacha"],
    ["code" => "MXN", "symbol" => "$", "name" => "Mexican peso"],
    ["code" => "MYR", "symbol" => "RM", "name" => "Malaysian ringgit"],
    ["code" => "MZM", "symbol" => "MTn", "name" => "Mozambican metical"],
    ["code" => "NAD", "symbol" => "N$", "name" => "Namibian dollar"],
    ["code" => "NGN", "symbol" => "₦", "name" => "Nigerian naira"],
    ["code" => "NIO", "symbol" => "C$", "name" => "Nicaraguan c\u00f3rdoba"],
    ["code" => "NOK", "symbol" => "kr", "name" => "Norwegian krone"],
    ["code" => "NPR", "symbol" => "NRs", "name" => "Nepalese rupee"],
    ["code" => "NZD", "symbol" => "NZ$", "name" => "New Zealand dollar"],
    ["code" => "OMR", "symbol" => "OMR", "name" => "Omani rial"],
    ["code" => "PAB", "symbol" => "B/.", "name" => "Panamanian balboa"],
    ["code" => "PEN", "symbol" => "S/.", "name" => "Peruvian nuevo sol"],
    ["code" => "PGK", "symbol" => "K", "name" => "Papua New Guinean kina"],
    ["code" => "PHP", "symbol" => "₱", "name" => "Philippine peso"],
    ["code" => "PKR", "symbol" => "Rs.", "name" => "Pakistani rupee"],
    ["code" => "PLN", "symbol" => "zł", "name" => "Polish zloty"],
    ["code" => "PYG", "symbol" => "₲", "name" => "Paraguayan guarani"],
    ["code" => "QAR", "symbol" => "QR", "name" => "Qatari riyal"],
    ["code" => "RON", "symbol" => "L", "name" => "Romanian leu"],
    ["code" => "RSD", "symbol" => "din.", "name" => "Serbian dinar"],
    ["code" => "RUB", "symbol" => "R", "name" => "Russian ruble"],
    ["code" => "SAR", "symbol" => "SR", "name" => "Saudi riyal"],
    ["code" => "SBD", "symbol" => "SI$", "name" => "Solomon Islands dollar"],
    ["code" => "SCR", "symbol" => "SR", "name" => "Seychellois rupee"],
    ["code" => "SDG", "symbol" => "SDG", "name" => "Sudanese pound"],
    ["code" => "SEK", "symbol" => "kr", "name" => "Swedish krona"],
    ["code" => "SGD", "symbol" => "S$", "name" => "Singapore dollar"],
    ["code" => "SHP", "symbol" => "£", "name" => "Saint Helena pound"],
    ["code" => "SLL", "symbol" => "Le", "name" => "Sierra Leonean leone"],
    ["code" => "SOS", "symbol" => "Sh.", "name" => "Somali shilling"],
    ["code" => "SRD", "symbol" => "$", "name" => "Surinamese dollar"],
    ["code" => "SYP", "symbol" => "LS", "name" => "Syrian pound"],
    ["code" => "SZL", "symbol" => "E", "name" => "Swazi lilangeni"],
    ["code" => "THB", "symbol" => "฿", "name" => "Thai baht"],
    ["code" => "TJS", "symbol" => "TJS", "name" => "Tajikistani somoni"],
    ["code" => "TMT", "symbol" => "m", "name" => "Turkmen manat"],
    ["code" => "TND", "symbol" => "DT", "name" => "Tunisian dinar"],
    ["code" => "TRY", "symbol" => "TRY", "name" => "Turkish new lira"],
    ["code" => "TTD", "symbol" => "TT$", "name" => "Trinidad and Tobago dollar"],
    ["code" => "TWD", "symbol" => "NT$", "name" => "New Taiwan dollar"],
    ["code" => "TZS", "symbol" => "TZS", "name" => "Tanzanian shilling"],
    ["code" => "UAH", "symbol" => "UAH", "name" => "Ukrainian hryvnia"],
    ["code" => "UGX", "symbol" => "USh", "name" => "Ugandan shilling"],
    ["code" => "USD", "symbol" => "$", "name" => "United States dollar"],
    ["code" => "UYU", "symbol" => '$U', "name" => "Uruguayan peso"],
    ["code" => "UZS", "symbol" => "UZS", "name" => "Uzbekistani som"],
    ["code" => "VEB", "symbol" => "Bs", "name" => "Venezuelan bolivar"],
    ["code" => "VND", "symbol" => "₫", "name" => "Vietnamese dong"],
    ["code" => "VUV", "symbol" => "VT", "name" => "Vanuatu vatu"],
    ["code" => "WST", "symbol" => "WS$", "name" => "Samoan tala"],
    ["code" => "XAF", "symbol" => "CFA", "name" => "Central African CFA franc"],
    ["code" => "XCD", "symbol" => "EC$", "name" => "East Caribbean dollar"],
    ["code" => "XDR", "symbol" => "SDR", "name" => "Special Drawing Rights"],
    ["code" => "XOF", "symbol" => "CFA", "name" => "West African CFA franc"],
    ["code" => "XPF", "symbol" => "F", "name" => "CFP franc"],
    ["code" => "YER", "symbol" => "YER", "name" => "Yemeni rial"],
    ["code" => "ZAR", "symbol" => "R", "name" => "South African rand"],
    ["code" => "ZMK", "symbol" => "ZK", "name" => "Zambian kwacha"],
    ["code" => "ZWR", "symbol" => "Z$", "name" => "Zimbabwean dollar"]
];

//countries
const GATEWAYS_COUNTRIES = [
    ["name" => 'Afghanistan', "code" => 'AF'],
    ["name" => 'Åland Islands', "code" => 'AX'],
    ["name" => 'Albania', "code" => 'AL'],
    ["name" => 'Algeria', "code" => 'DZ'],
    ["name" => 'American Samoa', "code" => 'AS'],
    ["name" => 'AndorrA', "code" => 'AD'],
    ["name" => 'Angola', "code" => 'AO'],
    ["name" => 'Anguilla', "code" => 'AI'],
    ["name" => 'Antarctica', "code" => 'AQ'],
    ["name" => 'Antigua and Barbuda', "code" => 'AG'],
    ["name" => 'Argentina', "code" => 'AR'],
    ["name" => 'Armenia', "code" => 'AM'],
    ["name" => 'Aruba', "code" => 'AW'],
    ["name" => 'Australia', "code" => 'AU'],
    ["name" => 'Austria', "code" => 'AT'],
    ["name" => 'Azerbaijan', "code" => 'AZ'],
    ["name" => 'Bahamas', "code" => 'BS'],
    ["name" => 'Bahrain', "code" => 'BH'],
    ["name" => 'Bangladesh', "code" => 'BD'],
    ["name" => 'Barbados', "code" => 'BB'],
    ["name" => 'Belarus', "code" => 'BY'],
    ["name" => 'Belgium', "code" => 'BE'],
    ["name" => 'Belize', "code" => 'BZ'],
    ["name" => 'Benin', "code" => 'BJ'],
    ["name" => 'Bermuda', "code" => 'BM'],
    ["name" => 'Bhutan', "code" => 'BT'],
    ["name" => 'Bolivia', "code" => 'BO'],
    ["name" => 'Bosnia and Herzegovina', "code" => 'BA'],
    ["name" => 'Botswana', "code" => 'BW'],
    ["name" => 'Bouvet Island', "code" => 'BV'],
    ["name" => 'Brazil', "code" => 'BR'],
    ["name" => 'British Indian Ocean Territory', "code" => 'IO'],
    ["name" => 'Brunei Darussalam', "code" => 'BN'],
    ["name" => 'Bulgaria', "code" => 'BG'],
    ["name" => 'Burkina Faso', "code" => 'BF'],
    ["name" => 'Burundi', "code" => 'BI'],
    ["name" => 'Cambodia', "code" => 'KH'],
    ["name" => 'Cameroon', "code" => 'CM'],
    ["name" => 'Canada', "code" => 'CA'],
    ["name" => 'Cape Verde', "code" => 'CV'],
    ["name" => 'Cayman Islands', "code" => 'KY'],
    ["name" => 'Central African Republic', "code" => 'CF'],
    ["name" => 'Chad', "code" => 'TD'],
    ["name" => 'Chile', "code" => 'CL'],
    ["name" => 'China', "code" => 'CN'],
    ["name" => 'Christmas Island', "code" => 'CX'],
    ["name" => 'Cocos (Keeling) Islands', "code" => 'CC'],
    ["name" => 'Colombia', "code" => 'CO'],
    ["name" => 'Comoros', "code" => 'KM'],
    ["name" => 'Congo', "code" => 'CG'],
    ["name" => 'Congo, The Democratic Republic of the', "code" => 'CD'],
    ["name" => 'Cook Islands', "code" => 'CK'],
    ["name" => 'Costa Rica', "code" => 'CR'],
    ["name" => 'Cote D\'Ivoire', "code" => 'CI'],
    ["name" => 'Croatia', "code" => 'HR'],
    ["name" => 'Cuba', "code" => 'CU'],
    ["name" => 'Cyprus', "code" => 'CY'],
    ["name" => 'Czech Republic', "code" => 'CZ'],
    ["name" => 'Denmark', "code" => 'DK'],
    ["name" => 'Djibouti', "code" => 'DJ'],
    ["name" => 'Dominica', "code" => 'DM'],
    ["name" => 'Dominican Republic', "code" => 'DO'],
    ["name" => 'Ecuador', "code" => 'EC'],
    ["name" => 'Egypt', "code" => 'EG'],
    ["name" => 'El Salvador', "code" => 'SV'],
    ["name" => 'Equatorial Guinea', "code" => 'GQ'],
    ["name" => 'Eritrea', "code" => 'ER'],
    ["name" => 'Estonia', "code" => 'EE'],
    ["name" => 'Ethiopia', "code" => 'ET'],
    ["name" => 'Falkland Islands (Malvinas)', "code" => 'FK'],
    ["name" => 'Faroe Islands', "code" => 'FO'],
    ["name" => 'Fiji', "code" => 'FJ'],
    ["name" => 'Finland', "code" => 'FI'],
    ["name" => 'France', "code" => 'FR'],
    ["name" => 'French Guiana', "code" => 'GF'],
    ["name" => 'French Polynesia', "code" => 'PF'],
    ["name" => 'French Southern Territories', "code" => 'TF'],
    ["name" => 'Gabon', "code" => 'GA'],
    ["name" => 'Gambia', "code" => 'GM'],
    ["name" => 'Georgia', "code" => 'GE'],
    ["name" => 'Germany', "code" => 'DE'],
    ["name" => 'Ghana', "code" => 'GH'],
    ["name" => 'Gibraltar', "code" => 'GI'],
    ["name" => 'Greece', "code" => 'GR'],
    ["name" => 'Greenland', "code" => 'GL'],
    ["name" => 'Grenada', "code" => 'GD'],
    ["name" => 'Guadeloupe', "code" => 'GP'],
    ["name" => 'Guam', "code" => 'GU'],
    ["name" => 'Guatemala', "code" => 'GT'],
    ["name" => 'Guernsey', "code" => 'GG'],
    ["name" => 'Guinea', "code" => 'GN'],
    ["name" => 'Guinea-Bissau', "code" => 'GW'],
    ["name" => 'Guyana', "code" => 'GY'],
    ["name" => 'Haiti', "code" => 'HT'],
    ["name" => 'Heard Island and Mcdonald Islands', "code" => 'HM'],
    ["name" => 'Holy See (Vatican City State)', "code" => 'VA'],
    ["name" => 'Honduras', "code" => 'HN'],
    ["name" => 'Hong Kong', "code" => 'HK'],
    ["name" => 'Hungary', "code" => 'HU'],
    ["name" => 'Iceland', "code" => 'IS'],
    ["name" => 'India', "code" => 'IN'],
    ["name" => 'Indonesia', "code" => 'ID'],
    ["name" => 'Iran, Islamic Republic Of', "code" => 'IR'],
    ["name" => 'Iraq', "code" => 'IQ'],
    ["name" => 'Ireland', "code" => 'IE'],
    ["name" => 'Isle of Man', "code" => 'IM'],
    ["name" => 'Israel', "code" => 'IL'],
    ["name" => 'Italy', "code" => 'IT'],
    ["name" => 'Jamaica', "code" => 'JM'],
    ["name" => 'Japan', "code" => 'JP'],
    ["name" => 'Jersey', "code" => 'JE'],
    ["name" => 'Jordan', "code" => 'JO'],
    ["name" => 'Kazakhstan', "code" => 'KZ'],
    ["name" => 'Kenya', "code" => 'KE'],
    ["name" => 'Kiribati', "code" => 'KI'],
    ["name" => 'Korea, Democratic People\'S Republic of', "code" => 'KP'],
    ["name" => 'Korea, Republic of', "code" => 'KR'],
    ["name" => 'Kuwait', "code" => 'KW'],
    ["name" => 'Kyrgyzstan', "code" => 'KG'],
    ["name" => 'Lao People\'S Democratic Republic', "code" => 'LA'],
    ["name" => 'Latvia', "code" => 'LV'],
    ["name" => 'Lebanon', "code" => 'LB'],
    ["name" => 'Lesotho', "code" => 'LS'],
    ["name" => 'Liberia', "code" => 'LR'],
    ["name" => 'Libyan Arab Jamahiriya', "code" => 'LY'],
    ["name" => 'Liechtenstein', "code" => 'LI'],
    ["name" => 'Lithuania', "code" => 'LT'],
    ["name" => 'Luxembourg', "code" => 'LU'],
    ["name" => 'Macao', "code" => 'MO'],
    ["name" => 'Macedonia, The Former Yugoslav Republic of', "code" => 'MK'],
    ["name" => 'Madagascar', "code" => 'MG'],
    ["name" => 'Malawi', "code" => 'MW'],
    ["name" => 'Malaysia', "code" => 'MY'],
    ["name" => 'Maldives', "code" => 'MV'],
    ["name" => 'Mali', "code" => 'ML'],
    ["name" => 'Malta', "code" => 'MT'],
    ["name" => 'Marshall Islands', "code" => 'MH'],
    ["name" => 'Martinique', "code" => 'MQ'],
    ["name" => 'Mauritania', "code" => 'MR'],
    ["name" => 'Mauritius', "code" => 'MU'],
    ["name" => 'Mayotte', "code" => 'YT'],
    ["name" => 'Mexico', "code" => 'MX'],
    ["name" => 'Micronesia, Federated States of', "code" => 'FM'],
    ["name" => 'Moldova, Republic of', "code" => 'MD'],
    ["name" => 'Monaco', "code" => 'MC'],
    ["name" => 'Mongolia', "code" => 'MN'],
    ["name" => 'Montserrat', "code" => 'MS'],
    ["name" => 'Morocco', "code" => 'MA'],
    ["name" => 'Mozambique', "code" => 'MZ'],
    ["name" => 'Myanmar', "code" => 'MM'],
    ["name" => 'Namibia', "code" => 'NA'],
    ["name" => 'Nauru', "code" => 'NR'],
    ["name" => 'Nepal', "code" => 'NP'],
    ["name" => 'Netherlands', "code" => 'NL'],
    ["name" => 'Netherlands Antilles', "code" => 'AN'],
    ["name" => 'New Caledonia', "code" => 'NC'],
    ["name" => 'New Zealand', "code" => 'NZ'],
    ["name" => 'Nicaragua', "code" => 'NI'],
    ["name" => 'Niger', "code" => 'NE'],
    ["name" => 'Nigeria', "code" => 'NG'],
    ["name" => 'Niue', "code" => 'NU'],
    ["name" => 'Norfolk Island', "code" => 'NF'],
    ["name" => 'Northern Mariana Islands', "code" => 'MP'],
    ["name" => 'Norway', "code" => 'NO'],
    ["name" => 'Oman', "code" => 'OM'],
    ["name" => 'Pakistan', "code" => 'PK'],
    ["name" => 'Palau', "code" => 'PW'],
    ["name" => 'Palestinian Territory, Occupied', "code" => 'PS'],
    ["name" => 'Panama', "code" => 'PA'],
    ["name" => 'Papua New Guinea', "code" => 'PG'],
    ["name" => 'Paraguay', "code" => 'PY'],
    ["name" => 'Peru', "code" => 'PE'],
    ["name" => 'Philippines', "code" => 'PH'],
    ["name" => 'Pitcairn', "code" => 'PN'],
    ["name" => 'Poland', "code" => 'PL'],
    ["name" => 'Portugal', "code" => 'PT'],
    ["name" => 'Puerto Rico', "code" => 'PR'],
    ["name" => 'Qatar', "code" => 'QA'],
    ["name" => 'Reunion', "code" => 'RE'],
    ["name" => 'Romania', "code" => 'RO'],
    ["name" => 'Russian Federation', "code" => 'RU'],
    ["name" => 'RWANDA', "code" => 'RW'],
    ["name" => 'Saint Helena', "code" => 'SH'],
    ["name" => 'Saint Kitts and Nevis', "code" => 'KN'],
    ["name" => 'Saint Lucia', "code" => 'LC'],
    ["name" => 'Saint Pierre and Miquelon', "code" => 'PM'],
    ["name" => 'Saint Vincent and the Grenadines', "code" => 'VC'],
    ["name" => 'Samoa', "code" => 'WS'],
    ["name" => 'San Marino', "code" => 'SM'],
    ["name" => 'Sao Tome and Principe', "code" => 'ST'],
    ["name" => 'Saudi Arabia', "code" => 'SA'],
    ["name" => 'Senegal', "code" => 'SN'],
    ["name" => 'Serbia and Montenegro', "code" => 'CS'],
    ["name" => 'Seychelles', "code" => 'SC'],
    ["name" => 'Sierra Leone', "code" => 'SL'],
    ["name" => 'Singapore', "code" => 'SG'],
    ["name" => 'Slovakia', "code" => 'SK'],
    ["name" => 'Slovenia', "code" => 'SI'],
    ["name" => 'Solomon Islands', "code" => 'SB'],
    ["name" => 'Somalia', "code" => 'SO'],
    ["name" => 'South Africa', "code" => 'ZA'],
    ["name" => 'South Georgia and the South Sandwich Islands', "code" => 'GS'],
    ["name" => 'Spain', "code" => 'ES'],
    ["name" => 'Sri Lanka', "code" => 'LK'],
    ["name" => 'Sudan', "code" => 'SD'],
    ["name" => 'Suriname', "code" => 'SR'],
    ["name" => 'Svalbard and Jan Mayen', "code" => 'SJ'],
    ["name" => 'Swaziland', "code" => 'SZ'],
    ["name" => 'Sweden', "code" => 'SE'],
    ["name" => 'Switzerland', "code" => 'CH'],
    ["name" => 'Syrian Arab Republic', "code" => 'SY'],
    ["name" => 'Taiwan, Province of China', "code" => 'TW'],
    ["name" => 'Tajikistan', "code" => 'TJ'],
    ["name" => 'Tanzania, United Republic of', "code" => 'TZ'],
    ["name" => 'Thailand', "code" => 'TH'],
    ["name" => 'Timor-Leste', "code" => 'TL'],
    ["name" => 'Togo', "code" => 'TG'],
    ["name" => 'Tokelau', "code" => 'TK'],
    ["name" => 'Tonga', "code" => 'TO'],
    ["name" => 'Trinidad and Tobago', "code" => 'TT'],
    ["name" => 'Tunisia', "code" => 'TN'],
    ["name" => 'Turkey', "code" => 'TR'],
    ["name" => 'Turkmenistan', "code" => 'TM'],
    ["name" => 'Turks and Caicos Islands', "code" => 'TC'],
    ["name" => 'Tuvalu', "code" => 'TV'],
    ["name" => 'Uganda', "code" => 'UG'],
    ["name" => 'Ukraine', "code" => 'UA'],
    ["name" => 'United Arab Emirates', "code" => 'AE'],
    ["name" => 'United Kingdom', "code" => 'GB'],
    ["name" => 'United States', "code" => 'US'],
    ["name" => 'United States Minor Outlying Islands', "code" => 'UM'],
    ["name" => 'Uruguay', "code" => 'UY'],
    ["name" => 'Uzbekistan', "code" => 'UZ'],
    ["name" => 'Vanuatu', "code" => 'VU'],
    ["name" => 'Venezuela', "code" => 'VE'],
    ["name" => 'Viet Nam', "code" => 'VN'],
    ["name" => 'Virgin Islands, British', "code" => 'VG'],
    ["name" => 'Virgin Islands, U.S.', "code" => 'VI'],
    ["name" => 'Wallis and Futuna', "code" => 'WF'],
    ["name" => 'Western Sahara', "code" => 'EH'],
    ["name" => 'Yemen', "code" => 'YE'],
    ["name" => 'Zambia', "code" => 'ZM'],
    ["name" => 'Zimbabwe', "code" => 'ZW']
];

//languages
const GATEWAYS_LANGUAGES = [
    ["code" => "ab", "name" => "Abkhaz", "nativeName" => "аҧсуа"],
    ["code" => "aa", "name" => "Afar", "nativeName" => "Afaraf"],
    ["code" => "af", "name" => "Afrikaans", "nativeName" => "Afrikaans"],
    ["code" => "ak", "name" => "Akan", "nativeName" => "Akan"],
    ["code" => "sq", "name" => "Albanian", "nativeName" => "Shqip"],
    ["code" => "am", "name" => "Amharic", "nativeName" => "አማርኛ"],
    ["code" => "ar", "name" => "Arabic", "nativeName" => "العربية"],
    ["code" => "an", "name" => "Aragonese", "nativeName" => "Aragonés"],
    ["code" => "hy", "name" => "Armenian", "nativeName" => "Հայերեն"],
    ["code" => "as", "name" => "Assamese", "nativeName" => "অসমীয়া"],
    ["code" => "av", "name" => "Avaric", "nativeName" => "авар мацӀ, магӀарул мацӀ"],
    ["code" => "ae", "name" => "Avestan", "nativeName" => "avesta"],
    ["code" => "ay", "name" => "Aymara", "nativeName" => "aymar aru"],
    ["code" => "az", "name" => "Azerbaijani", "nativeName" => "azərbaycan dili"],
    ["code" => "bm", "name" => "Bambara", "nativeName" => "bamanankan"],
    ["code" => "ba", "name" => "Bashkir", "nativeName" => "башҡорт теле"],
    ["code" => "eu", "name" => "Basque", "nativeName" => "euskara, euskera"],
    ["code" => "be", "name" => "Belarusian", "nativeName" => "Беларуская"],
    ["code" => "bn", "name" => "Bengali", "nativeName" => "বাংলা"],
    ["code" => "bh", "name" => "Bihari", "nativeName" => "भोजपुरी"],
    ["code" => "bi", "name" => "Bislama", "nativeName" => "Bislama"],
    ["code" => "bs", "name" => "Bosnian", "nativeName" => "bosanski jezik"],
    ["code" => "br", "name" => "Breton", "nativeName" => "brezhoneg"],
    ["code" => "bg", "name" => "Bulgarian", "nativeName" => "български език"],
    ["code" => "my", "name" => "Burmese", "nativeName" => "ဗမာစာ"],
    ["code" => "ca", "name" => "Catalan; Valencian", "nativeName" => "Català"],
    ["code" => "ch", "name" => "Chamorro", "nativeName" => "Chamoru"],
    ["code" => "ce", "name" => "Chechen", "nativeName" => "нохчийн мотт"],
    ["code" => "ny", "name" => "Chichewa; Chewa; Nyanja", "nativeName" => "chiCheŵa, chinyanja"],
    ["code" => "zh", "name" => "Chinese", "nativeName" => "中文 (Zhōngwén), 汉语, 漢語"],
    ["code" => "cv", "name" => "Chuvash", "nativeName" => "чӑваш чӗлхи"],
    ["code" => "kw", "name" => "Cornish", "nativeName" => "Kernewek"],
    ["code" => "co", "name" => "Corsican", "nativeName" => "corsu, lingua corsa"],
    ["code" => "cr", "name" => "Cree", "nativeName" => "ᓀᐦᐃᔭᐍᐏᐣ"],
    ["code" => "hr", "name" => "Croatian", "nativeName" => "hrvatski"],
    ["code" => "cs", "name" => "Czech", "nativeName" => "česky, čeština"],
    ["code" => "da", "name" => "Danish", "nativeName" => "dansk"],
    ["code" => "dv", "name" => "Divehi; Dhivehi; Maldivian;", "nativeName" => "ދިވެހި"],
    ["code" => "nl", "name" => "Dutch", "nativeName" => "Nederlands, Vlaams"],
    ["code" => "en", "name" => "English", "nativeName" => "English"],
    ["code" => "eo", "name" => "Esperanto", "nativeName" => "Esperanto"],
    ["code" => "et", "name" => "Estonian", "nativeName" => "eesti, eesti keel"],
    ["code" => "ee", "name" => "Ewe", "nativeName" => "Eʋegbe"],
    ["code" => "fo", "name" => "Faroese", "nativeName" => "føroyskt"],
    ["code" => "fj", "name" => "Fijian", "nativeName" => "vosa Vakaviti"],
    ["code" => "fi", "name" => "Finnish", "nativeName" => "suomi, suomen kieli"],
    ["code" => "fr", "name" => "French", "nativeName" => "français, langue française"],
    ["code" => "ff", "name" => "Fula; Fulah; Pulaar; Pular", "nativeName" => "Fulfulde, Pulaar, Pular"],
    ["code" => "gl", "name" => "Galician", "nativeName" => "Galego"],
    ["code" => "ka", "name" => "Georgian", "nativeName" => "ქართული"],
    ["code" => "de", "name" => "German", "nativeName" => "Deutsch"],
    ["code" => "el", "name" => "Greek, Modern", "nativeName" => "Ελληνικά"],
    ["code" => "gn", "name" => "Guaraní", "nativeName" => "Avañeẽ"],
    ["code" => "gu", "name" => "Gujarati", "nativeName" => "ગુજરાતી"],
    ["code" => "ht", "name" => "Haitian; Haitian Creole", "nativeName" => "Kreyòl ayisyen"],
    ["code" => "ha", "name" => "Hausa", "nativeName" => "Hausa, هَوُسَ"],
    ["code" => "he", "name" => "Hebrew (modern)", "nativeName" => "עברית"],
    ["code" => "hz", "name" => "Herero", "nativeName" => "Otjiherero"],
    ["code" => "hi", "name" => "Hindi", "nativeName" => "हिन्दी, हिंदी"],
    ["code" => "ho", "name" => "Hiri Motu", "nativeName" => "Hiri Motu"],
    ["code" => "hu", "name" => "Hungarian", "nativeName" => "Magyar"],
    ["code" => "ia", "name" => "Interlingua", "nativeName" => "Interlingua"],
    ["code" => "id", "name" => "Indonesian", "nativeName" => "Bahasa Indonesia"],
    ["code" => "ie", "name" => "Interlingue", "nativeName" => "Originally called Occidental; then Interlingue after WWII"],
    ["code" => "ga", "name" => "Irish", "nativeName" => "Gaeilge"],
    ["code" => "ig", "name" => "Igbo", "nativeName" => "Asụsụ Igbo"],
    ["code" => "ik", "name" => "Inupiaq", "nativeName" => "Iñupiaq, Iñupiatun"],
    ["code" => "io", "name" => "Ido", "nativeName" => "Ido"],
    ["code" => "is", "name" => "Icelandic", "nativeName" => "Íslenska"],
    ["code" => "it", "name" => "Italian", "nativeName" => "Italiano"],
    ["code" => "iu", "name" => "Inuktitut", "nativeName" => "ᐃᓄᒃᑎᑐᑦ"],
    ["code" => "ja", "name" => "Japanese", "nativeName" => "日本語 (にほんご／にっぽんご)"],
    ["code" => "jv", "name" => "Javanese", "nativeName" => "basa Jawa"],
    ["code" => "kl", "name" => "Kalaallisut, Greenlandic", "nativeName" => "kalaallisut, kalaallit oqaasii"],
    ["code" => "kn", "name" => "Kannada", "nativeName" => "ಕನ್ನಡ"],
    ["code" => "kr", "name" => "Kanuri", "nativeName" => "Kanuri"],
    ["code" => "ks", "name" => "Kashmiri", "nativeName" => "कश्मीरी, كشميري‎"],
    ["code" => "kk", "name" => "Kazakh", "nativeName" => "Қазақ тілі"],
    ["code" => "km", "name" => "Khmer", "nativeName" => "ភាសាខ្មែរ"],
    ["code" => "ki", "name" => "Kikuyu, Gikuyu", "nativeName" => "Gĩkũyũ"],
    ["code" => "rw", "name" => "Kinyarwanda", "nativeName" => "Ikinyarwanda"],
    ["code" => "ky", "name" => "Kirghiz, Kyrgyz", "nativeName" => "кыргыз тили"],
    ["code" => "kv", "name" => "Komi", "nativeName" => "коми кыв"],
    ["code" => "kg", "name" => "Kongo", "nativeName" => "KiKongo"],
    ["code" => "ko", "name" => "Korean", "nativeName" => "한국어 (韓國語), 조선말 (朝鮮語)"],
    ["code" => "ku", "name" => "Kurdish", "nativeName" => "Kurdî, كوردی‎"],
    ["code" => "kj", "name" => "Kwanyama, Kuanyama", "nativeName" => "Kuanyama"],
    ["code" => "la", "name" => "Latin", "nativeName" => "latine, lingua latina"],
    ["code" => "lb", "name" => "Luxembourgish, Letzeburgesch", "nativeName" => "Lëtzebuergesch"],
    ["code" => "lg", "name" => "Luganda", "nativeName" => "Luganda"],
    ["code" => "li", "name" => "Limburgish, Limburgan, Limburger", "nativeName" => "Limburgs"],
    ["code" => "ln", "name" => "Lingala", "nativeName" => "Lingála"],
    ["code" => "lo", "name" => "Lao", "nativeName" => "ພາສາລາວ"],
    ["code" => "lt", "name" => "Lithuanian", "nativeName" => "lietuvių kalba"],
    ["code" => "lu", "name" => "Luba-Katanga", "nativeName" => ""],
    ["code" => "lv", "name" => "Latvian", "nativeName" => "latviešu valoda"],
    ["code" => "gv", "name" => "Manx", "nativeName" => "Gaelg, Gailck"],
    ["code" => "mk", "name" => "Macedonian", "nativeName" => "македонски јазик"],
    ["code" => "mg", "name" => "Malagasy", "nativeName" => "Malagasy fiteny"],
    ["code" => "ms", "name" => "Malay", "nativeName" => "bahasa Melayu, بهاس ملايو‎"],
    ["code" => "ml", "name" => "Malayalam", "nativeName" => "മലയാളം"],
    ["code" => "mt", "name" => "Maltese", "nativeName" => "Malti"],
    ["code" => "mi", "name" => "Māori", "nativeName" => "te reo Māori"],
    ["code" => "mr", "name" => "Marathi (Marāṭhī)", "nativeName" => "मराठी"],
    ["code" => "mh", "name" => "Marshallese", "nativeName" => "Kajin M̧ajeļ"],
    ["code" => "mn", "name" => "Mongolian", "nativeName" => "монгол"],
    ["code" => "na", "name" => "Nauru", "nativeName" => "Ekakairũ Naoero"],
    ["code" => "nv", "name" => "Navajo, Navaho", "nativeName" => "Diné bizaad, Dinékʼehǰí"],
    ["code" => "nb", "name" => "Norwegian Bokmål", "nativeName" => "Norsk bokmål"],
    ["code" => "nd", "name" => "North Ndebele", "nativeName" => "isiNdebele"],
    ["code" => "ne", "name" => "Nepali", "nativeName" => "नेपाली"],
    ["code" => "ng", "name" => "Ndonga", "nativeName" => "Owambo"],
    ["code" => "nn", "name" => "Norwegian Nynorsk", "nativeName" => "Norsk nynorsk"],
    ["code" => "no", "name" => "Norwegian", "nativeName" => "Norsk"],
    ["code" => "ii", "name" => "Nuosu", "nativeName" => "ꆈꌠ꒿ Nuosuhxop"],
    ["code" => "nr", "name" => "South Ndebele", "nativeName" => "isiNdebele"],
    ["code" => "oc", "name" => "Occitan", "nativeName" => "Occitan"],
    ["code" => "oj", "name" => "Ojibwe, Ojibwa", "nativeName" => "ᐊᓂᔑᓈᐯᒧᐎᓐ"],
    ["code" => "cu", "name" => "Old Church Slavonic, Church Slavic, Church Slavonic, Old Bulgarian, Old Slavonic", "nativeName" => "ѩзыкъ словѣньскъ"],
    ["code" => "om", "name" => "Oromo", "nativeName" => "Afaan Oromoo"],
    ["code" => "or", "name" => "Oriya", "nativeName" => "ଓଡ଼ିଆ"],
    ["code" => "os", "name" => "Ossetian, Ossetic", "nativeName" => "ирон æвзаг"],
    ["code" => "pa", "name" => "Panjabi, Punjabi", "nativeName" => "ਪੰਜਾਬੀ, پنجابی‎"],
    ["code" => "pi", "name" => "Pāli", "nativeName" => "पाऴि"],
    ["code" => "fa", "name" => "Persian", "nativeName" => "فارسی"],
    ["code" => "pl", "name" => "Polish", "nativeName" => "polski"],
    ["code" => "ps", "name" => "Pashto, Pushto", "nativeName" => "پښتو"],
    ["code" => "pt", "name" => "Portuguese", "nativeName" => "Português"],
    ["code" => "qu", "name" => "Quechua", "nativeName" => "Runa Simi, Kichwa"],
    ["code" => "rm", "name" => "Romansh", "nativeName" => "rumantsch grischun"],
    ["code" => "rn", "name" => "Kirundi", "nativeName" => "kiRundi"],
    ["code" => "ro", "name" => "Romanian, Moldavian, Moldovan", "nativeName" => "română"],
    ["code" => "ru", "name" => "Russian", "nativeName" => "русский язык"],
    ["code" => "sa", "name" => "Sanskrit (Saṁskṛta)", "nativeName" => "संस्कृतम्"],
    ["code" => "sc", "name" => "Sardinian", "nativeName" => "sardu"],
    ["code" => "sd", "name" => "Sindhi", "nativeName" => "सिन्धी, سنڌي، سندھی‎"],
    ["code" => "se", "name" => "Northern Sami", "nativeName" => "Davvisámegiella"],
    ["code" => "sm", "name" => "Samoan", "nativeName" => "gagana faa Samoa"],
    ["code" => "sg", "name" => "Sango", "nativeName" => "yângâ tî sängö"],
    ["code" => "sr", "name" => "Serbian", "nativeName" => "српски језик"],
    ["code" => "gd", "name" => "Scottish Gaelic; Gaelic", "nativeName" => "Gàidhlig"],
    ["code" => "sn", "name" => "Shona", "nativeName" => "chiShona"],
    ["code" => "si", "name" => "Sinhala, Sinhalese", "nativeName" => "සිංහල"],
    ["code" => "sk", "name" => "Slovak", "nativeName" => "slovenčina"],
    ["code" => "sl", "name" => "Slovene", "nativeName" => "slovenščina"],
    ["code" => "so", "name" => "Somali", "nativeName" => "Soomaaliga, af Soomaali"],
    ["code" => "st", "name" => "Southern Sotho", "nativeName" => "Sesotho"],
    ["code" => "es", "name" => "Spanish; Castilian", "nativeName" => "español, castellano"],
    ["code" => "su", "name" => "Sundanese", "nativeName" => "Basa Sunda"],
    ["code" => "sw", "name" => "Swahili", "nativeName" => "Kiswahili"],
    ["code" => "ss", "name" => "Swati", "nativeName" => "SiSwati"],
    ["code" => "sv", "name" => "Swedish", "nativeName" => "svenska"],
    ["code" => "ta", "name" => "Tamil", "nativeName" => "தமிழ்"],
    ["code" => "te", "name" => "Telugu", "nativeName" => "తెలుగు"],
    ["code" => "tg", "name" => "Tajik", "nativeName" => "тоҷикӣ, toğikī, تاجیکی‎"],
    ["code" => "th", "name" => "Thai", "nativeName" => "ไทย"],
    ["code" => "ti", "name" => "Tigrinya", "nativeName" => "ትግርኛ"],
    ["code" => "bo", "name" => "Tibetan Standard, Tibetan, Central", "nativeName" => "བོད་ཡིག"],
    ["code" => "tk", "name" => "Turkmen", "nativeName" => "Türkmen, Түркмен"],
    ["code" => "tl", "name" => "Tagalog", "nativeName" => "Wikang Tagalog, ᜏᜒᜃᜅ᜔ ᜆᜄᜎᜓᜄ᜔"],
    ["code" => "tn", "name" => "Tswana", "nativeName" => "Setswana"],
    ["code" => "to", "name" => "Tonga (Tonga Islands)", "nativeName" => "faka Tonga"],
    ["code" => "tr", "name" => "Turkish", "nativeName" => "Türkçe"],
    ["code" => "ts", "name" => "Tsonga", "nativeName" => "Xitsonga"],
    ["code" => "tt", "name" => "Tatar", "nativeName" => "татарча, tatarça, تاتارچا‎"],
    ["code" => "tw", "name" => "Twi", "nativeName" => "Twi"],
    ["code" => "ty", "name" => "Tahitian", "nativeName" => "Reo Tahiti"],
    ["code" => "ug", "name" => "Uighur, Uyghur", "nativeName" => "Uyƣurqə, ئۇيغۇرچە‎"],
    ["code" => "uk", "name" => "Ukrainian", "nativeName" => "українська"],
    ["code" => "ur", "name" => "Urdu", "nativeName" => "اردو"],
    ["code" => "uz", "name" => "Uzbek", "nativeName" => "zbek, Ўзбек, أۇزبېك‎"],
    ["code" => "ve", "name" => "Venda", "nativeName" => "Tshivenḓa"],
    ["code" => "vi", "name" => "Vietnamese", "nativeName" => "Tiếng Việt"],
    ["code" => "vo", "name" => "Volapük", "nativeName" => "Volapük"],
    ["code" => "wa", "name" => "Walloon", "nativeName" => "Walon"],
    ["code" => "cy", "name" => "Welsh", "nativeName" => "Cymraeg"],
    ["code" => "wo", "name" => "Wolof", "nativeName" => "Wollof"],
    ["code" => "fy", "name" => "Western Frisian", "nativeName" => "Frysk"],
    ["code" => "xh", "name" => "Xhosa", "nativeName" => "isiXhosa"],
    ["code" => "yi", "name" => "Yiddish", "nativeName" => "ייִדיש"],
    ["code" => "yo", "name" => "Yoruba", "nativeName" => "Yorùbá"],
    ["code" => "za", "name" => "Zhuang, Chuang", "nativeName" => "Saɯ cueŋƅ, Saw cuengh"]
];

const TELEPHONE_CODES = [
    ["name" => 'UK (+44)', "code" => '+44'],
    ["name" => 'USA (+1)', "code" => '+1'],
    ["name" => 'Algeria (+213)', "code" => '+213'],
    ["name" => 'Andorra (+376)', "code" => '+376'],
    ["name" => 'Angola (+244)', "code" => '+244'],
    ["name" => 'Anguilla (+1264)', "code" => '+1264'],
    ["name" => 'Antigua & Barbuda (+1268)', "code" => '+1268'],
    ["name" => 'Argentina (+54)', "code" => '+54'],
    ["name" => 'Armenia (+374)', "code" => '+374'],
    ["name" => 'Aruba (+297)', "code" => '+297'],
    ["name" => 'Australia (+61)', "code" => '+61'],
    ["name" => 'Austria (+43)', "code" => '+43'],
    ["name" => 'Azerbaijan (+994)', "code" => '+994'],
    ["name" => 'Bahamas (+1242)', "code" => '+1242'],
    ["name" => 'Bahrain (+973)', "code" => '+973'],
    ["name" => 'Bangladesh (+880)', "code" => '+880'],
    ["name" => 'Barbados (+1246)', "code" => '+1246'],
    ["name" => 'Belarus (+375)', "code" => '+375'],
    ["name" => 'Belgium (+32)', "code" => '+32'],
    ["name" => 'Belize (+501)', "code" => '+501'],
    ["name" => 'Benin (+229)', "code" => '+229'],
    ["name" => 'Bermuda (+1441)', "code" => '+1441'],
    ["name" => 'Bhutan (+975)', "code" => '+975'],
    ["name" => 'Bolivia (+591)', "code" => '+591'],
    ["name" => 'Bosnia Herzegovina (+387)', "code" => '+387'],
    ["name" => 'Botswana (+267)', "code" => '+267'],
    ["name" => 'Brazil (+55)', "code" => '+55'],
    ["name" => 'Brunei (+673)', "code" => '+673'],
    ["name" => 'Bulgaria (+359)', "code" => '+359'],
    ["name" => 'Burkina Faso (+226)', "code" => '+226'],
    ["name" => 'Burundi (+257)', "code" => '+257'],
    ["name" => 'Cambodia (+855)', "code" => '+855'],
    ["name" => 'Cameroon (+237)', "code" => '+237'],
    ["name" => 'Canada (+1)', "code" => '+1'],
    ["name" => 'Cape Verde Islands (+238)', "code" => '+238'],
    ["name" => 'Cayman Islands (+1345)', "code" => '+1345'],
    ["name" => 'Central African Republic (+236)', "code" => '+236'],
    ["name" => 'Chile (+56)', "code" => '+56'],
    ["name" => 'China (+86)', "code" => '+86'],
    ["name" => 'Colombia (+57)', "code" => '+57'],
    ["name" => 'Comoros (+269)', "code" => '+269'],
    ["name" => 'Congo (+242)', "code" => '+242'],
    ["name" => 'Cook Islands (+682)', "code" => '+682'],
    ["name" => 'Costa Rica (+506)', "code" => '+506'],
    ["name" => 'Croatia (+385)', "code" => '+385'],
    ["name" => 'Cuba (+53)', "code" => '+53'],
    ["name" => 'Cyprus North (+90392)', "code" => '+90392'],
    ["name" => 'Cyprus South (+357)', "code" => '+357'],
    ["name" => 'Czech Republic (+42)', "code" => '+42'],
    ["name" => 'Denmark (+45)', "code" => '+45'],
    ["name" => 'Djibouti (+253)', "code" => '+253'],
    ["name" => 'Dominica (+1767)', "code" => '+1767'],
    ["name" => 'Dominican Republic (+1809)', "code" => '+1809'],
    ["name" => 'Ecuador (+593)', "code" => '+593'],
    ["name" => 'Egypt (+20)', "code" => '+20'],
    ["name" => 'El Salvador (+503)', "code" => '+503'],
    ["name" => 'Equatorial Guinea (+240)', "code" => '+240'],
    ["name" => 'Eritrea (+291)', "code" => '+291'],
    ["name" => 'Estonia (+372)', "code" => '+372'],
    ["name" => 'Ethiopia (+251)', "code" => '+251'],
    ["name" => 'Falkland Islands (+500)', "code" => '+500'],
    ["name" => 'Faroe Islands (+298)', "code" => '+298'],
    ["name" => 'Fiji (+679)', "code" => '+679'],
    ["name" => 'Finland (+358)', "code" => '+358'],
    ["name" => 'France (+33)', "code" => '+33'],
    ["name" => 'French Guiana (+594)', "code" => '+594'],
    ["name" => 'French Polynesia (+689)', "code" => '+689'],
    ["name" => 'Gabon (+241)', "code" => '+241'],
    ["name" => 'Gambia (+220)', "code" => '+220'],
    ["name" => 'Georgia (+7880)', "code" => '+7880'],
    ["name" => 'Germany (+49)', "code" => '+49'],
    ["name" => 'Ghana (+233)', "code" => '+233'],
    ["name" => 'Gibraltar (+350)', "code" => '+350'],
    ["name" => 'Greece (+30)', "code" => '+30'],
    ["name" => 'Greenland (+299)', "code" => '+299'],
    ["name" => 'Grenada (+1473)', "code" => '+1473'],
    ["name" => 'Guadeloupe (+590)', "code" => '+590'],
    ["name" => 'Guam (+671)', "code" => '+671'],
    ["name" => 'Guatemala (+502)', "code" => '+502'],
    ["name" => 'Guinea (+224)', "code" => '+224'],
    ["name" => 'Guinea - Bissau (+245)', "code" => '+245'],
    ["name" => 'Guyana (+592)', "code" => '+592'],
    ["name" => 'Haiti (+509)', "code" => '+509'],
    ["name" => 'Honduras (+504)', "code" => '+504'],
    ["name" => 'Hong Kong (+852)', "code" => '+852'],
    ["name" => 'Hungary (+36)', "code" => '+36'],
    ["name" => 'Iceland (+354)', "code" => '+354'],
    ["name" => 'India (+91)', "code" => '+91'],
    ["name" => 'Indonesia (+62)', "code" => '+62'],
    ["name" => 'Iran (+98)', "code" => '+98'],
    ["name" => 'Iraq (+964)', "code" => '+964'],
    ["name" => 'Ireland (+353)', "code" => '+353'],
    ["name" => 'Israel (+972)', "code" => '+972'],
    ["name" => 'Italy (+39)', "code" => '+39'],
    ["name" => 'Jamaica (+1876)', "code" => '+1876'],
    ["name" => 'Japan (+81)', "code" => '+81'],
    ["name" => 'Jordan (+962)', "code" => '+962'],
    ["name" => 'Kazakhstan (+7)', "code" => '+7'],
    ["name" => 'Kenya (+254)', "code" => '+254'],
    ["name" => 'Kiribati (+686)', "code" => '+686'],
    ["name" => 'Korea North (+850)', "code" => '+850'],
    ["name" => 'Korea South (+82)', "code" => '+82'],
    ["name" => 'Kuwait (+965)', "code" => '+965'],
    ["name" => 'Kyrgyzstan (+996)', "code" => '+996'],
    ["name" => 'Laos (+856)', "code" => '+856'],
    ["name" => 'Latvia (+371)', "code" => '+371'],
    ["name" => 'Lebanon (+961)', "code" => '+961'],
    ["name" => 'Lesotho (+266)', "code" => '+266'],
    ["name" => 'Liberia (+231)', "code" => '+231'],
    ["name" => 'Libya (+218)', "code" => '+218'],
    ["name" => 'Liechtenstein (+417)', "code" => '+417'],
    ["name" => 'Lithuania (+370)', "code" => '+370'],
    ["name" => 'Luxembourg (+352)', "code" => '+352'],
    ["name" => 'Macao (+853)', "code" => '+853'],
    ["name" => 'Macedonia (+389)', "code" => '+389'],
    ["name" => 'Madagascar (+261)', "code" => '+261'],
    ["name" => 'Malawi (+265)', "code" => '+265'],
    ["name" => 'Malaysia (+60)', "code" => '+60'],
    ["name" => 'Maldives (+960)', "code" => '+960'],
    ["name" => 'Mali (+223)', "code" => '+223'],
    ["name" => 'Malta (+356)', "code" => '+356'],
    ["name" => 'Marshall Islands (+692)', "code" => '+692'],
    ["name" => 'Martinique (+596)', "code" => '+596'],
    ["name" => 'Mauritania (+222)', "code" => '+222'],
    ["name" => 'Mayotte (+269)', "code" => '+269'],
    ["name" => 'Mexico (+52)', "code" => '+52'],
    ["name" => 'Micronesia (+691)', "code" => '+691'],
    ["name" => 'Moldova (+373)', "code" => '+373'],
    ["name" => 'Monaco (+377)', "code" => '+377'],
    ["name" => 'Montserrat (+1664)', "code" => '+1664'],
    ["name" => 'Morocco (+212)', "code" => '+212'],
    ["name" => 'Mozambique (+258)', "code" => '+258'],
    ["name" => 'Myanmar (+95)', "code" => '+95'],
    ["name" => 'Namibia (+264)', "code" => '+264'],
    ["name" => 'Nauru (+674)', "code" => '+674'],
    ["name" => 'Nepal (+977)', "code" => '+977'],
    ["name" => 'Netherlands (+31)', "code" => '+31'],
    ["name" => 'New Caledonia (+687)', "code" => '+687'],
    ["name" => 'New Zealand (+64)', "code" => '+64'],
    ["name" => 'Nicaragua (+505)', "code" => '+505'],
    ["name" => 'Niger (+227)', "code" => '+227'],
    ["name" => 'Nigeria (+234)', "code" => '+234'],
    ["name" => 'Niue (+683)', "code" => '+683'],
    ["name" => 'Norfolk Islands (+672)', "code" => '+672'],
    ["name" => 'Northern Marianas (+670)', "code" => '+670'],
    ["name" => 'Norway (+47)', "code" => '+47'],
    ["name" => 'Oman (+968)', "code" => '+968'],
    ["name" => 'Palau (+680)', "code" => '+680'],
    ["name" => 'Panama (+507)', "code" => '+507'],
    ["name" => 'Papua New Guinea (+675)', "code" => '+675'],
    ["name" => 'Paraguay (+595)', "code" => '+595'],
    ["name" => 'Peru (+51)', "code" => '+51'],
    ["name" => 'Philippines (+63)', "code" => '+63'],
    ["name" => 'Poland (+48)', "code" => '+48'],
    ["name" => 'Portugal (+351)', "code" => '+351'],
    ["name" => 'Qatar (+974)', "code" => '+974'],
    ["name" => 'Reunion (+262)', "code" => '+262'],
    ["name" => 'Romania (+40)', "code" => '+40'],
    ["name" => 'Russia (+7)', "code" => '+7'],
    ["name" => 'Rwanda (+250)', "code" => '+250'],
    ["name" => 'San Marino (+378)', "code" => '+378'],
    ["name" => 'Sao Tome & Principe (+239)', "code" => '+239'],
    ["name" => 'Saudi Arabia (+966)', "code" => '+966'],
    ["name" => 'Senegal (+221)', "code" => '+221'],
    ["name" => 'Serbia (+381)', "code" => '+381'],
    ["name" => 'Seychelles (+248)', "code" => '+248'],
    ["name" => 'Sierra Leone (+232)', "code" => '+232'],
    ["name" => 'Singapore (+65)', "code" => '+65'],
    ["name" => 'Slovak Republic (+421)', "code" => '+421'],
    ["name" => 'Slovenia (+386)', "code" => '+386'],
    ["name" => 'Solomon Islands (+677)', "code" => '+677'],
    ["name" => 'Somalia (+252)', "code" => '+252'],
    ["name" => 'South Africa (+27)', "code" => '+27'],
    ["name" => 'Spain (+34)', "code" => '+34'],
    ["name" => 'Sri Lanka (+94)', "code" => '+94'],
    ["name" => 'St. Helena (+290)', "code" => '+290'],
    ["name" => 'St. Kitts (+1869)', "code" => '+1869'],
    ["name" => 'St. Lucia (+1758)', "code" => '+1758'],
    ["name" => 'Sudan (+249)', "code" => '+249'],
    ["name" => 'Suriname (+597)', "code" => '+597'],
    ["name" => 'Swaziland (+268)', "code" => '+268'],
    ["name" => 'Sweden (+46)', "code" => '+46'],
    ["name" => 'Switzerland (+41)', "code" => '+41'],
    ["name" => 'Syria (+963)', "code" => '+963'],
    ["name" => 'Taiwan (+886)', "code" => '+886'],
    ["name" => 'Tajikstan (+7)', "code" => '+7'],
    ["name" => 'Thailand (+66)', "code" => '+66'],
    ["name" => 'Togo (+228)', "code" => '+228'],
    ["name" => 'Tonga (+676)', "code" => '+676'],
    ["name" => 'Trinidad & Tobago (+1868)', "code" => '+1868'],
    ["name" => 'Tunisia (+216)', "code" => '+216'],
    ["name" => 'Turkey (+90)', "code" => '+90'],
    ["name" => 'Turkmenistan (+7)', "code" => '+7'],
    ["name" => 'Turkmenistan (+993)', "code" => '+993'],
    ["name" => 'Turks & Caicos Islands (+1649)', "code" => '+1649'],
    ["name" => 'Tuvalu (+688)', "code" => '+688'],
    ["name" => 'Uganda (+256)', "code" => '+256'],
    ["name" => 'Ukraine (+380)', "code" => '+380'],
    ["name" => 'United Arab Emirates (+971)', "code" => '+971'],
    ["name" => 'Uruguay (+598)', "code" => '+598'],
    ["name" => 'Uzbekistan (+7)', "code" => '+7'],
    ["name" => 'Vanuatu (+678)', "code" => '+678'],
    ["name" => 'Vatican City (+379)', "code" => '+379'],
    ["name" => 'Venezuela (+58)', "code" => '+58'],
    ["name" => 'Vietnam (+84)', "code" => '+84'],
    ["name" => 'Virgin Islands - British (+1284)', "code" => '+1284'],
    ["name" => 'Virgin Islands - US (+1340)', "code" => '+1340'],
    ["name" => 'Wallis & Futuna (+681)', "code" => '+681'],
    ["name" => 'Yemen (North)(+969)', "code" => '+969'],
    ["name" => 'Yemen (South)(+967)', "code" => '+967'],
    ["name" => 'Zambia (+260)', "code" => '+260'],
    ["name" => 'Zimbabwe (+263)', "code" => '+263'],
];
